import { SxProps } from '@mui/material';
import { Theme } from '@mui/material/styles';

// Define the types directly to avoid circular dependencies
type TableSelectNumSelected = number;
type TableSelectRowCount = number | undefined;

export type TableHeadPropsType<TRow> = {
  headLabel: {
    label: any;
    id: any;
    orderName?: string;
    align?: string | undefined;
    width?: string;
    minWidth?: string;
  }[];

  selectable?: {
    numSelected?: TableSelectNumSelected;
    rowCount?: TableSelectRowCount;
    handleSelectAllRows: (checked: boolean) => void;
  };

  sx?: SxProps<Theme>;
};
