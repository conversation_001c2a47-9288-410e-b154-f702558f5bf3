import type { BoxProps } from '@mui/material/Box';

import Box from '@mui/material/Box';
import Container from '@mui/material/Container';

import { useSettingsContext } from 'src/components/settings';
import { layoutClasses } from '../classes';

// ----------------------------------------------------------------------

export function Main({ children, sx, ...other }: BoxProps) {
  const settings = useSettingsContext();

  return (
    <Box
      component="main"
      className={layoutClasses.main}
      sx={{
        display: 'flex',
        flex: '1 1 auto',
        flexDirection: 'column',
        ...sx,
      }}
      {...other}
    >
      <Container
        maxWidth={settings.compactLayout ? false : 'xl'}
        sx={{
          mt: 2,
          pt: { xs: 3, md: 4, lg: 5 },
          pb: { xs: 3, md: 4, lg: 5 },
        }}
      >
        {children}
      </Container>
    </Box>
  );
}
