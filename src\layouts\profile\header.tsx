import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, IconButton, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';

import { useResponsive } from 'src/hooks/use-responsive';
import { usePathname } from 'src/routes/hooks';

import { Logo } from 'src/components/logo';
import { Iconify } from 'src/components/iconify';
import { AccountMenu } from 'src/layouts/components/account-menu';
import { NotificationsMenu } from 'src/layouts/components/notifications-menu';
import { _notifications } from 'src/_mock/_notifications';
import { GoogleSearchbar } from '../components/google-searchbar';

// ----------------------------------------------------------------------

interface ProfileHeaderProps {
  onOpenNav: () => void;
}

export function ProfileHeader({ onOpenNav }: ProfileHeaderProps) {
  const theme = useTheme();
  const lgUp = useResponsive('up', 'lg');
  const pathname = usePathname();
  const { t } = useTranslation();

  // Check if the current path is the knowledge-base path
  const isKnowledgeBasePath = pathname?.includes('/dashboard/profile/knowledge-base');

  const handleMarkAllAsRead = () => {
    console.log('Profile header: Marking all notifications as read');
    // In a real app, you would call an API to mark all notifications as read
  };

  const handleViewAllNotifications = () => {
    console.log('Profile header: Navigate to all notifications page');
    // In a real app, you would navigate to the notifications page
  };

  return (
    <AppBar
      sx={{
        boxShadow: 'none',
        height: 64,
        zIndex: theme.zIndex.appBar + 1,
        backgroundColor: 'transparent',
        backdropFilter: 'blur(6px)',
        WebkitBackdropFilter: 'blur(6px)', // For Safari
        borderBottom: `solid 1px ${theme.palette.divider}`,
        transition: theme.transitions.create(['height'], {
          duration: theme.transitions.duration.shorter,
        }),
      }}
    >
      <Toolbar
        sx={{
          height: 1,
          px: { lg: 5 },
        }}
      >
        {!lgUp && !isKnowledgeBasePath && (
          <IconButton onClick={onOpenNav} sx={{ mr: 1 }}>
            <Iconify icon="eva:menu-2-fill" />
          </IconButton>
        )}

        {lgUp && (
          <Box sx={{ width: '20%', mb: '20px', display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Logo href="/dashboard" sx={{ mt: '20px' }} />
            <Typography variant="h5" sx={{ mt: '20px' }}>
              {t('components.header.workForces')}
            </Typography>
          </Box>
        )}

        <GoogleSearchbar data-slot="searchbar" />

        <Box sx={{ flexGrow: 1, mx: '10px' }} />

        <Stack direction="row" alignItems="center" spacing={{ xs: 0.5, sm: 1 }}>
          <NotificationsMenu
            data={_notifications}
            onMarkAllAsRead={handleMarkAllAsRead}
            onViewAll={handleViewAllNotifications}
          />
          {/* <SettingsButton /> */}
          <AccountMenu />
        </Stack>
      </Toolbar>
    </AppBar>
  );
}
