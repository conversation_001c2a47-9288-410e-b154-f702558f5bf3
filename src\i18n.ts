import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import { en } from './locales/en';
import { ar } from './locales/ar';

// Get the stored language or default to 'en'
const storedLang = localStorage.getItem('i18nextLng');
const lng = storedLang ? storedLang.split('-')[0] : 'en';

// Resources
const resources = {
  en: { translation: en },
  ar: { translation: ar },
};

// Initialize i18n with Language Detector
i18n
  .use(LanguageDetector) // Add the language detector
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    lng,
    debug: true, // Enable debug mode to see console logs
    interpolation: {
      escapeValue: false, // React already handles XSS
    },
    detection: {
      order: ['localStorage', 'cookie', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'], // Save language choice in localStorage and cookies
    },
    react: {
      useSuspense: false, // Disable suspense to avoid issues
    },
  });

// Log the current language and resources for debugging
console.log('Current language:', i18n.language);
console.log('Available languages:', Object.keys(resources));
console.log('Translation resources loaded:', resources);

// Log specific translation keys for debugging
console.log('teams.title in English:', i18n.getResource('en', 'translation', 'teams.title'));
console.log('teams.title in Arabic:', i18n.getResource('ar', 'translation', 'teams.title'));
console.log('components.teams.title in English:', i18n.getResource('en', 'translation', 'components.teams.title'));
console.log('components.teams.title in Arabic:', i18n.getResource('ar', 'translation', 'components.teams.title'));

export default i18n;
