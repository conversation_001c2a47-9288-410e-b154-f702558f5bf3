import {
  Card,
  CardHeader,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  Tooltip,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { NoData } from 'src/components/no-data';
import { AppButton } from '../../common';
import TableHeadCustom from '../table-head-custom';
import TablePaginationCustom from '../table-pagination-custom';
import TableSelectedAction from '../table-selected-action';
import TableSkeleton from '../table-skeleton';
import AppTableRow from './components/app-table-row';
import AppTableTabs from './components/app-table-tabs';
import { AppTablePropsType } from './types/app-table';

export const AppTable = <TRow,>({
  title,
  headLabels,
  dataCount,
  buttons,
  isLoading,
  data,
  columns,
  filtersProp,
  searchProps,
  table,
  select,
  sx,
  tabsProps,
  noDataLabel,
}: AppTablePropsType<TRow>) => {
  return (
    <Card sx={{ bgcolor: 'background.default', ...sx! }}>
      {title && <CardHeader title={title} sx={{ textAlign: 'center' }} />}
      {title && <Divider sx={{ mt: 3 }} />}

      <TableContainer>
        {table && (
          <TableSelectedAction
            dense={table.dense}
            numSelected={table.selected.length}
            rowCount={select?.rowCount ? +select.rowCount : 0}
            onSelectAllRows={(checked) => table.onSelectAllRows(checked, [])}
            action={select?.selectedRowsActions?.map((action) => {
              if (action.type === 'iconButton') {
                return (
                  <Tooltip title={action?.tooltip}>
                    <IconButton onClick={action.handler}>
                      <Iconify
                        icon={action.icon}
                        width="27"
                        height="27"
                        color={action?.color || '#FFAB8B'}
                      />
                    </IconButton>
                  </Tooltip>
                );
              }
              if (action.type === 'button') {
                return (
                  <AppButton
                    label={action.label}
                    onClick={action.handler}
                    color={action.color}
                    fullWidth={false}
                    isLoading={action.isLoading}
                  />
                );
              }
              return null;
            })}
          />
        )}
        <Scrollbar>
          {tabsProps && <AppTableTabs {...tabsProps} />}

          <Table>
            <TableHeadCustom<TRow>
              headLabel={headLabels}
              selectable={{
                ...select,
                handleSelectAllRows: select?.handleSelectAllRows?.(
                  data?.map((d) => d[select.idPath as unknown as keyof TRow] as string)
                )!,
              }}
            />
            <TableBody>
              {isLoading
                ? [...Array(table?.rowsPerPage)].map((i, index) => <TableSkeleton key={index} />)
                : data?.map((row, index) => {
                    return (
                      <AppTableRow<typeof row>
                        key={index}
                        data={row}
                        columns={columns}
                        selectRow={{
                          handleSelectRow: select?.handleSelectRow!,
                          table: table!,
                          id: (row as any)[select?.idPath],
                        }}
                        index={index}
                      />
                    );
                  })}

              {!isLoading && !data?.length && <NoData isTableView label={noDataLabel} />}
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>
      {data && table && (
        <TablePaginationCustom
          count={dataCount}
          page={+table.page}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      )}
    </Card>
  );
};

export default AppTable;
