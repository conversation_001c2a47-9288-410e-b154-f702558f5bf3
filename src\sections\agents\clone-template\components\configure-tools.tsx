import { useEffect } from 'react';
import { LoadingScreen } from 'src/components/loading-screen';
import { useSearchParams } from 'src/routes/hooks';

const ConfigureTools = () => {
  const searchParams = useSearchParams();
  const state = searchParams.get('state');
  const code = searchParams.get('code');

  useEffect(() => {
    if (state && code) {
      // Check if this is running in a popup window
      if (window.opener && !window.opener.closed) {
        // Send message to parent window
        window.opener.postMessage(
          {
            type: 'OAUTH_SUCCESS',
            state,
            code,
          },
          window.location.origin
        );

        // Close the popup
        window.close();
      } else {
        // Fallback: if not in popup, store in localStorage and navigate
        localStorage.setItem('state', state);
        localStorage.setItem('code', code);
        const templateId = localStorage.getItem('templateId');
        window.location.href = `/dashboard/agents/clone/${templateId}`;
      }
    }
  }, [state, code]);

  return <LoadingScreen />;
};

export default ConfigureTools;
