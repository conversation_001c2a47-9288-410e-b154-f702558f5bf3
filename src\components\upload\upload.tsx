import { useDropzone } from 'react-dropzone';
import { Box, Stack, Typography, styled, alpha } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { FileThumbnail } from 'src/components/file-thumbnail';
import { UploadIllustration } from 'src/assets/illustrations';
import { UploadProps } from './types';

// ----------------------------------------------------------------------

const StyledDropZone = styled('div')(({ theme }) => ({
  outline: 'none',
  cursor: 'pointer',
  overflow: 'hidden',
  position: 'relative',
  padding: theme.spacing(5),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create('padding'),
  backgroundColor: theme.palette.background.neutral,
  border: `1px dashed ${alpha(theme.palette.grey[500], 0.32)}`,
  '&:hover': {
    opacity: 0.72,
  },
}));

// ----------------------------------------------------------------------

export function Upload({
  disabled,
  multiple = false,
  error,
  helperText,
  //
  files,
  onDelete,
  onRemove,
  onUpload,
  onRemoveAll,
  //
  sx,
  thumbnail,
  placeholder,
  ...other
}: UploadProps) {
  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    multiple,
    disabled,
    ...other,
  });

  const hasFile = !!files?.length;

  const isError = isDragReject || !!error;

  return (
    <Box sx={{ width: 1, position: 'relative', ...sx }}>
      <StyledDropZone
        {...getRootProps()}
        sx={{
          ...(isDragActive && {
            opacity: 0.72,
          }),
          ...(isError && {
            color: 'error.main',
            bgcolor: 'error.lighter',
            borderColor: 'error.light',
          }),
          ...(disabled && {
            opacity: 0.48,
            pointerEvents: 'none',
          }),
          ...(hasFile && {
            padding: '12% 0',
          }),
        }}
      >
        <input {...getInputProps()} />

        {placeholder || (
          <Stack
            spacing={2}
            alignItems="center"
            justifyContent="center"
            direction={{
              xs: 'column',
              md: 'row',
            }}
            sx={{
              width: 1,
              textAlign: {
                xs: 'center',
                md: 'left',
              },
            }}
          >
            <UploadIllustration sx={{ width: 120 }} />

            <Stack sx={{ p: 1 }} spacing={1}>
              <Typography gutterBottom variant="h5">
                Drop or Select file
              </Typography>

              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Drop files here or click
                <Box
                  component="span"
                  sx={{ mx: 0.5, color: 'primary.main', textDecoration: 'underline' }}
                >
                  browse
                </Box>
                through your machine
              </Typography>
            </Stack>
          </Stack>
        )}
      </StyledDropZone>

      {helperText && (
        <Box sx={{ mt: 1.5 }}>
          <Typography
            variant="caption"
            sx={{
              mx: 'auto',
              display: 'block',
              textAlign: 'center',
              color: 'text.secondary',
              ...(isError && {
                color: 'error.main',
              }),
            }}
          >
            {helperText}
          </Typography>
        </Box>
      )}

      {hasFile && (
        <>
          <Box sx={{ my: 3 }}>
            <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 1 }}>
              {files.map((file, index) => {
                const isString = typeof file === 'string';

                const fileName = isString
                  ? file.split('/').pop()
                  : file.name;

                return (
                  <Box
                    key={index}
                    sx={{
                      p: 1,
                      m: 0.5,
                      width: 80,
                      height: 80,
                      borderRadius: 1.25,
                      overflow: 'hidden',
                      position: 'relative',
                      display: 'inline-flex',
                      border: (theme) => `solid 1px ${theme.palette.divider}`,
                      ...(thumbnail && {
                        width: 'auto',
                        height: 'auto',
                        maxWidth: 'calc(100% - 8px)',
                      }),
                    }}
                  >
                    <FileThumbnail
                      tooltip
                      imageView
                      file={file}
                      sx={{ width: 1, height: 1 }}
                      onRemove={
                        onRemove
                          ? () => onRemove(file)
                          : onDelete
                          ? () => onDelete(file)
                          : undefined
                      }
                    />
                  </Box>
                );
              })}
            </Stack>

            <Stack direction="row" justifyContent="flex-end" spacing={1.5}>
              {onRemoveAll && (
                <Box
                  component="button"
                  onClick={onRemoveAll}
                  sx={{
                    p: 0,
                    m: 0,
                    border: 0,
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    typography: 'body2',
                    color: 'text.secondary',
                    bgcolor: 'transparent',
                    '&:hover': { color: 'text.primary' },
                  }}
                >
                  <Iconify icon="eva:trash-2-outline" sx={{ mr: 0.5 }} />
                  Remove all
                </Box>
              )}

              {onUpload && (
                <Box
                  component="button"
                  onClick={onUpload}
                  sx={{
                    p: 0,
                    m: 0,
                    border: 0,
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    typography: 'body2',
                    color: 'text.secondary',
                    bgcolor: 'transparent',
                    '&:hover': { color: 'text.primary' },
                  }}
                >
                  <Iconify icon="eva:cloud-upload-fill" sx={{ mr: 0.5 }} />
                  Upload
                </Box>
              )}
            </Stack>
          </Box>
        </>
      )}
    </Box>
  );
}
