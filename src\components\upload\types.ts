import { SxProps, Theme } from '@mui/material/styles';
import { DropzoneOptions } from 'react-dropzone';

// ----------------------------------------------------------------------

export interface UploadProps extends DropzoneOptions {
  error?: boolean;
  disabled?: boolean;
  sx?: SxProps<Theme>;
  thumbnail?: boolean;
  placeholder?: React.ReactNode;
  helperText?: React.ReactNode;
  disableMultiple?: boolean;
  files?: (File | string)[];
  onDelete?: (file: File | string) => void;
  onRemove?: (file: File | string) => void;
  onUpload?: VoidFunction;
  onRemoveAll?: VoidFunction;
}
