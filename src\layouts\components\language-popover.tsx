import type { IconButtonProps } from '@mui/material/IconButton';
import { useTranslation } from 'react-i18next';

import { m } from 'framer-motion';
import { useState, useCallback, useEffect } from 'react';

import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

import { varHover } from 'src/components/animate';
import { Iconify } from 'src/components/iconify';
import { usePopover, CustomPopover } from 'src/components/custom-popover';
import { useSettingsContext } from 'src/components/settings';

// ----------------------------------------------------------------------

// Default language options
const LANGUAGES = [
  {
    value: 'en',
    label: 'English',
    icon: 'emojione:flag-for-united-states',
  },
  {
    value: 'ar',
    label: 'العربية',
    icon: 'emojione:flag-for-saudi-arabia',
  },
];

// ----------------------------------------------------------------------

export type LanguagePopoverProps = IconButtonProps & {
  data?: {
    value: string;
    label: string;
    icon: string;
  }[];
};

export function LanguagePopover({ data = LANGUAGES, sx, ...other }: LanguagePopoverProps) {
  const popover = usePopover();
  const { i18n } = useTranslation();
  const settings = useSettingsContext();

  // Get the base language code (en or ar) from the current language
  const getCurrentLang = () => {
    const fullLang = i18n.language || localStorage.getItem('i18nextLng') || 'en';
    return fullLang.split('-')[0]; // Handle cases like 'en-US'
  };

  const [currentLang, setCurrentLang] = useState(getCurrentLang());

  // Update current language when i18n.language changes
  useEffect(() => {
    setCurrentLang(getCurrentLang());
  }, [i18n.language]);

  // Find the current language option
  const selectedLang = data.find((lang) => lang.value === currentLang) || data[0];

  const handleChangeLang = useCallback(
    (newLang: string) => {
      // Update direction based on language
      if (newLang === 'ar') {
        settings.onUpdateField('direction', 'rtl');
      } else {
        settings.onUpdateField('direction', 'ltr');
      }

      // Change language in i18n
      i18n.changeLanguage(newLang);

      // Update state and close popover
      setCurrentLang(newLang);
      popover.onClose();

      // Log for debugging
      console.log('Language changed to:', newLang);
      console.log('Current i18n language:', i18n.language);
    },
    [i18n, popover, settings]
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={popover.onOpen}
        sx={{
          p: 0,
          width: 40,
          height: 40,
          ...(popover.open && { bgcolor: 'action.selected' }),
          ...sx,
        }}
        {...other}
      >
        <Iconify icon={selectedLang.icon} width={28} />
      </IconButton>

      <CustomPopover open={popover.open} anchorEl={popover.anchorEl} onClose={popover.onClose}>
        <MenuList sx={{ width: 160, minHeight: 72 }}>
          {data.map((option) => (
            <MenuItem
              key={option.value}
              selected={option.value === currentLang}
              onClick={() => handleChangeLang(option.value)}
              sx={{ py: 1 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon={option.icon} width={24} sx={{ mr: 1 }} />
                <Typography variant="body2">{option.label}</Typography>
              </Box>
            </MenuItem>
          ))}
        </MenuList>
      </CustomPopover>
    </>
  );
}
