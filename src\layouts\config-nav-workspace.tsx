import { CONF<PERSON> } from 'src/config-global';
import { useTranslation } from 'react-i18next';

// ----------------------------------------------------------------------

export function useWorkspaces() {
  const { t } = useTranslation();

  const workspaces = [
  {
    id: 'team-1',
    name: t('components.workspaces.team1'),
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-1.webp`,
    plan: t('components.workspaces.free'),
  },
  {
    id: 'team-2',
    name: t('components.workspaces.team2'),
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-2.webp`,
    plan: t('components.workspaces.pro'),
  },
  {
    id: 'team-3',
    name: t('components.workspaces.team3'),
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-3.webp`,
    plan: t('components.workspaces.pro'),
  },
];

  return workspaces;
}

// For backward compatibility
export const _workspaces = [
  {
    id: 'team-1',
    name: 'Team 1',
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-1.webp`,
    plan: 'Free',
  },
  {
    id: 'team-2',
    name: 'Team 2',
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-2.webp`,
    plan: 'Pro',
  },
  {
    id: 'team-3',
    name: 'Team 3',
    logo: `${CONFIG.site.basePath}/assets/icons/workspaces/logo-3.webp`,
    plan: 'Pro',
  },
];
