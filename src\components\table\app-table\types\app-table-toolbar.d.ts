import {
  AutocompleteChangeDetails,
  AutocompleteChangeReason,
  SelectChangeEvent,
} from '@mui/material';
import {
  DateTimeValidationError,
  PickerChangeHandlerContext,
} from '@mui/x-date-pickers/models';
import { ReturnType } from 'libs/shared-lib/src/lib/hooks';
import { SetQuery } from 'use-query-params';
import { TableProps } from '../../types';

interface TextFieldFilterProps {
  title: string;
  type: 'text';
  onChange: React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  value: string;
}

interface SwitchFieldFilterProps {
  title: string;
  type: 'boolean';
  onClick?: React.MouseEventHandler<HTMLButtonElement> | undefined;
  value: string;
}

interface SelectFieldFilterProps {
  title: string;
  type: 'select';
  onChange: (e: SelectChangeEvent<unknown>) => void;
  options: { label: string; value: string | undefined; id?: string }[];
  value: string;
}

interface AutoCompleteFieldFilterProps {
  type: 'autocomplete';
  title: string;
  options: { label: string; value: string | undefined; id?: string }[];
  onChange:
    | ((
        event: React.SyntheticEvent,
        value: unknown,
        reason: AutocompleteChangeReason,
        details?: AutocompleteChangeDetails<unknown> | undefined,
      ) => void)
    | undefined;
  value: string;
}

interface DateFieldFilterProps {
  title: string;
  type: 'date';
  onChange:
    | ((
        value: unknown,
        context: PickerChangeHandlerContext<DateTimeValidationError>,
      ) => void)
    | undefined;
  onChangeOperator: (e: SelectChangeEvent<unknown>) => void;
  date: string;
  operator: string;
}

type AppTableButtonType = {
  path?: string;
  permissionName?: string;
  onClick?: () => void;
  label?: string;
  id?: string;
};

export type AppTableSearchType = {
  searchValue: string;
  setQuery: SetQuery<any>;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export type AppTableFiltersType = {
  reset: any;
  openFilters: ReturnType;
  filters: (
    | TextFieldFilterProps
    | SwitchFieldFilterProps
    | SelectFieldFilterProps
    | DateFieldFilterProps
    | AutoCompleteFieldFilterProps
  )[];
  areFiltersApplied: boolean;
};

export type IAppTableToolbarProps = {
  buttons?: AppTableButtonType[];

  // Table Search
  searchProps?: AppTableSearchType;

  // Table filters
  filtersProp?: AppTableFiltersType;

  isLoading?: boolean;
  data?: any;
  pagination?: TableProps;
};
