import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const AgentsEndpoints = {
  list: '/agents',
  details: '/agents',
};

// Define the Category interface

export interface Agent {
  id: number;
  userId: number;
  templateId: number;
  specialRequest: string;
  template: {
    id: number;
    creatorId: number;
    name: string;
    description: string;
    categoryId: number;
    type: string; // or more specific: "SINGLE" | "OTHER_TYPE" if you know all possible values
    model: string; // or more specific if you know all possible model values
    status: string; // or more specific: "ACTIVE" | "INACTIVE" etc.
    createdAt: string; // or Date if you parse it
    updatedAt: string; // or Date if you parse it
    systemMessage: string;
    visibility: string; // or more specific: "PUBLIC" | "PRIVATE" etc.
    category: {
      id: number;
      name: string;
      description: string;
      icon: string;
      theme: string;
      createdAt: string; // or Date
      updatedAt: string; // or Date
    };
  };
}

export interface AgentsResponse {
  agents: Agent[];
  total: number;
}

// Define filters interface for agents API
export interface AgentFilters {
  type?: string;
  categoryId?: number;
  status?: string;
  'template-name'?: string;
  take?: number;
  skip?: number;
}

export interface Agents {
  Agents: any;
}

// Define the API response structure

// Create a hook to use the Agentss API
export const useAgentsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss with optional filters
  const useGetAgentss = (filters?: AgentFilters) => {
    return apiServices.useGetListService<AgentsResponse, AgentFilters>({
      url: AgentsEndpoints.list,
      params: filters,
    });
  };

  // Get a single Agents by ID
  const useGetAgents = (id: string) => {
    return apiServices.useGetItemService<Agents>({
      url: AgentsEndpoints.details,
      id,
    });
  };

  // Create a new Agents
  const useCreateAgents = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<Agents, any>({
      url: AgentsEndpoints.list,
      onSuccess,
    });
  };

  // Update a Agents
  const useUpdateAgents = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<Agents>({
      url: AgentsEndpoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a Agents
  const useDeleteAgents = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number>({
      url: AgentsEndpoints.details,
      urlAfterSuccess: AgentsEndpoints.details + 'list',
      onSuccess,
    });
  };

  return {
    useGetAgentss,
    useGetAgents,
    useCreateAgents,
    useUpdateAgents,
    useDeleteAgents,
  };
};
