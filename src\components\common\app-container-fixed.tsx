import { Grid, Stack, SxProps, Typography } from '@mui/material';
import Container from '@mui/material/Container';
import { ReactNode } from 'react';
import { Helmet } from 'react-helmet-async';
import { CustomBreadcrumbs, BreadcrumbsLinkProps } from '../custom-breadcrumbs';
import { useSettingsContext } from '../settings';
import { AppButton, IAppButtonProps } from './app-button';

interface IAppContainerProps {
  children: ReactNode;
  // Optional props
  pageTitle?: string;
  routeLinks?: BreadcrumbsLinkProps[];
  isLoading?: boolean; // This prop is ignored
  sx?: SxProps;
  noContainer?: boolean;
  title?: string;
  buttons?: IAppButtonProps[];
}

export const AppContainerFixed = ({
  pageTitle,
  routeLinks,
  children,
  isLoading, // This prop is ignored
  sx,
  noContainer,
  title,
  buttons,
}: IAppContainerProps) => {
  const settings = useSettingsContext();

  const view = (
    <Stack spacing={1} sx={{ ...sx }}>
      {(title || buttons) && (
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h4">{title}</Typography>

          <Stack direction="row" spacing={1} alignItems="center">
            {buttons?.map((button, index) => (
              <AppButton key={index} fullWidth={false} {...button} />
            ))}
          </Stack>
        </Stack>
      )}
      {routeLinks !== undefined && <CustomBreadcrumbs links={routeLinks ?? []} />}
      {children}
    </Stack>
  );

  // Always render content immediately, never show loading screen
  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
      </Helmet>

      {noContainer ? (
        view
      ) : (
        <Container
          maxWidth={settings.compactLayout ? false : 'xl'}
          sx={{ mt: 2 }}
        >
          {view}
        </Container>
      )}
    </>
  );
};

export default AppContainerFixed;
