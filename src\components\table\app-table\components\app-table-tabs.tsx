import { Card, Tab, Tabs } from '@mui/material';
import { SyntheticEvent, useState } from 'react';
import { AppTablePropsType, TabsType } from '../types/app-table';

const AppTableTabs = ({ tabs, changeTabsHandler }: TabsType) => {
  const [currentTabValue, setCurrentTabValue] = useState<string | null>(tabs?.currentTabValue!);

  const changeHandler = (event: SyntheticEvent<Element, Event>, value: string | null) => {
    setCurrentTabValue(value);
    if (changeTabsHandler) {
      changeTabsHandler(event, value);
    }
  };

  return (
    <Card>
      <Tabs
        value={currentTabValue}
        onChange={changeHandler}
        sx={{
          px: 2,
          padding: 0.75,
          borderRadius: 0,
          '.MuiTabs-indicator': {
            display: 'none',
          },
        }}
      >
        {tabs!?.tabs.map((tab) => (
          <Tab
            key={tab.value}
            value={tab.value}
            label={tab.label}
            sx={(theme) => {
              return {
                color:
                  tab.value === currentTabValue
                    ? theme.palette.primary.darker
                    : theme.palette.background.neutral,
                // backgroundColor:
                //   tab.value === currentTabValue ? 'primary.dark' : 'background.neutral',
                borderBottom: `1px solid ${
                  tab.value === currentTabValue
                    ? theme.palette.primary.darker
                    : theme.palette.background.neutral
                }`,
                boxShadow: 'none',
                fontWeight: 500,
                width: 125,
              };
            }}
          />
        ))}
      </Tabs>
    </Card>
  );
};

export default AppTableTabs;
