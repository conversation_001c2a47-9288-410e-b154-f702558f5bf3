import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { DashboardLayout, DashboardLayoutNoSidebar } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';
import TeamTemplatesChat from 'src/sections/teams/chat/agents-chat';

import { AuthGuard } from 'src/auth/guard';
import { paths } from '../paths';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('src/pages/dashboard/one'));

// agents pages
const AgentsPage = lazy(() => import('src/pages/dashboard/agents/agents-page'));
const TemplatesPage = lazy(() => import('src/pages/dashboard/agents/templates-page'));
const AgentsClonePage = lazy(() => import('src/pages/dashboard/agents/agents-clone-page'));
const ConfigureToolsPage = lazy(() => import('src/pages/dashboard/agents/configure-tools-page'));

// team templates pages
const MyTeamsTemplatesPage = lazy(() => import('src/pages/dashboard/teams/my-templates-page'));
const TemplatesTeamPage = lazy(() => import('src/pages/dashboard/teams/templates-page'));
const TeamTemplatessClonePage = lazy(() => import('src/pages/dashboard/teams/temlates-clone-page'));
const ConfigureteamToolsPage = lazy(
  () => import('src/pages/dashboard/agents/configure-tools-page')
);

const AgentsChatPage = lazy(() => import('src/pages/dashboard/agents/agents-chat-page'));
const KnowledgeBasePage = lazy(
  () => import('src/pages/dashboard/knowledge-base/knowledge-base-page')
);
const SettingsPage = lazy(() => import('src/pages/dashboard/settings/settings-page'));

// const PageTwo = lazy(() => import('src/pages/dashboard/two'));

// ----------------------------------------------------------------------

// to choose the redirect url to the team or agent clone
const agentOrTeam = localStorage.getItem('typeOfConfigureation');
const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

const layoutContentNoSidebar = (
  <DashboardLayoutNoSidebar>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayoutNoSidebar>
);

export const dashboardRoutes = [
  // Routes with sidebar
  {
    path: paths.dashboard.root,
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      // agents pages
      { path: paths.dashboard.agents.root, element: <TemplatesPage /> },
      { path: paths.dashboard.agents.agents, element: <AgentsPage /> },
      { path: paths.dashboard.agents.templates, element: <TemplatesPage /> },

      // team templates
      { path: paths.dashboard.teams.root, element: <TemplatesTeamPage /> },
      { path: paths.dashboard.teams.my_team_templates, element: <MyTeamsTemplatesPage /> },
      { path: paths.dashboard.teams.templates, element: <TemplatesTeamPage /> },

      {
        path: paths.dashboard.knowledgeBase,
        element: <KnowledgeBasePage />,
      },
      {
        path: paths.dashboard.settings,
        element: <SettingsPage />,
      },
    ],
  },
  // Routes without sidebar
  {
    path: paths.dashboard.root,
    element: CONFIG.auth.skip ? (
      <>{layoutContentNoSidebar}</>
    ) : (
      <AuthGuard>{layoutContentNoSidebar}</AuthGuard>
    ),
    children: [
      // pages for agents
      { path: paths.dashboard.agents.clone(':id'), element: <AgentsClonePage /> },
      { path: paths.dashboard.agents.chat(':id', ':agentId', ':cid'), element: <AgentsChatPage /> },
      {
        path: paths.dashboard.agents.configureTool,
        element: agentOrTeam === 'team' ? <ConfigureteamToolsPage /> : <ConfigureToolsPage />,
      },

      // pages for team templates
      { path: paths.dashboard.teams.clone(':id'), element: <TeamTemplatessClonePage /> },
      {
        path: paths.dashboard.teams.chat(':id', ':agentId', ':cid'),
        element: <TeamTemplatesChat />,
      },
      // { path: paths.dashboard.agents.configureTool, element:  },

      // {
      //   path: paths.dashboard.agents.chat_messages_events(':id', ':agentId', ':cid'),
      //   element: <ChatMessagesEventPage />,
      // },
    ],
  },
  // {
  //   path: 'dashboard/profile',
  //   element: CONFIG.auth.skip ? (
  //     <ProfileLayout>
  //       <Suspense fallback={<LoadingScreen />}>
  //         <Outlet />
  //       </Suspense>
  //     </ProfileLayout>
  //   ) : (
  //     <AuthGuard>
  //       <ProfileLayout>
  //         <Suspense fallback={<LoadingScreen />}>
  //           <Outlet />
  //         </Suspense>
  //       </ProfileLayout>
  //     </AuthGuard>
  //   ),
  //   children: [
  //     { element: <ProfilePage />, index: true },
  //     { path: 'knowledge-base', element: <KnowledgeBasePage /> },
  //     { path: 'preferences', element: <PreferencesPage /> },
  //     { path: 'settings', element: <SettingsPage /> },
  //   ],
  // },
];
