import { AxiosInstance } from 'axios';

export const useGetPictureurl = (axiosInstance: AxiosInstance) => {
  const generateDownloadUrl = async (
    fileKey: string,
    endpoint: string = '/files/download-url'
  ): Promise<string> => {
    try {
      const response = await axiosInstance.get(endpoint, {
        params: { key: fileKey },
      });

      return response.data.url;
    } catch (error) {
      console.error('Error generating download URL:', error);
      throw error; // Re-throw to let the caller handle it
    }
  };

  return {
    generateDownloadUrl,
  };
};
