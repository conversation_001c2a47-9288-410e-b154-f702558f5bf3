import { useState, useEffect, useMemo } from 'react';
import { useTemplatesApi, Template, TemplateFilters } from 'src/services/api/use-templates-api';
import {
  useCategoriesApi,
  Category,
  CategoriesResponse,
} from 'src/services/api/use-categories-api';
import { useDebounce } from 'src/hooks/use-debounce';
import { table } from 'console';
import { useTable } from 'src/components/table';

// Filter options for table view
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const TOOLS_FILTERS = ['All', 'Web Search', 'Calculator', 'Code Interpreter'];
const MODEL_FILTERS = ['All', 'GPT-4', 'GPT-3.5', 'Claude', 'Gemini'];
const STATUS_FILTERS = ['All', 'ACTIVE', 'INACTIVE'];

export const useAgentView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Template[]>([]);
  const [selectedTab, setSelectedTab] = useState(0); // 0 = Cards (PUBLIC), 1 = Table (PRIVATE)

  const table = useTable();

  // Debounce search query for API calls (400ms delay)
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Cards view filters for public templates
  const [selectedCardsTypeFilter, setSelectedCardsTypeFilter] = useState(0);
  const [selectedCardsCategoryFilter, setSelectedCardsCategoryFilter] = useState(0);

  // Table filters for private templates
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);
  const [selectedToolsFilter, setSelectedToolsFilter] = useState(0);
  const [selectedModelFilter, setSelectedModelFilter] = useState(0);
  const [selectedStatusFilter, setSelectedStatusFilter] = useState(0);

  // Use the templates API hook to fetch data with visibility filter
  const { useGetTemplates } = useTemplatesApi();

  // Use the categories API hook to fetch categories
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetCategories();

  // Create dynamic category filters with useMemo to prevent infinite re-renders
  const CATEGORY_FILTERS = useMemo(() => {
    if (
      !categoriesData ||
      !categoriesData.categories ||
      !Array.isArray(categoriesData.categories)
    ) {
      return ['All'];
    }
    return ['All', ...categoriesData.categories.map((category) => category.name)];
  }, [categoriesData]);

  // Determine current visibility based on selected tab
  const currentVisibility = selectedTab === 0 ? 'PUBLIC' : 'PRIVATE';

  // Build filters for API call
  const apiFilters: TemplateFilters = {
    visibility: currentVisibility,
    take: table.rowsPerPage,
    skip: table.page,
    // Add search query if present (backend search)
    ...(debouncedSearchQuery && { name: debouncedSearchQuery }),
    ...(selectedTab === 0 && {
      // Cards view filters (public templates)
      ...(selectedCardsTypeFilter !== 0 && { type: TYPE_FILTERS[selectedCardsTypeFilter] }),
      ...(selectedCardsCategoryFilter !== 0 && {
        categoryId: categoriesData?.categories?.[selectedCardsCategoryFilter - 1]?.id
          ? parseInt(categoriesData.categories[selectedCardsCategoryFilter - 1].id, 10)
          : undefined,
      }),
    }),
    ...(selectedTab === 1 && {
      // Table view filters (private templates)
      ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
      ...(selectedCategoryFilter !== 0 && {
        categoryId: categoriesData?.categories?.[selectedCategoryFilter - 1]?.id
          ? parseInt(categoriesData.categories[selectedCategoryFilter - 1].id, 10)
          : undefined,
      }),
      ...(selectedToolsFilter !== 0 && { tools: TOOLS_FILTERS[selectedToolsFilter] }),
      ...(selectedModelFilter !== 0 && { model: MODEL_FILTERS[selectedModelFilter] }),
      ...(selectedStatusFilter !== 0 && { status: STATUS_FILTERS[selectedStatusFilter] }),
    }),
  };

  const { data: templatesResponse, isLoading, error, refetch } = useGetTemplates(apiFilters);

  // Extract templates from the response
  const templates = templatesResponse?.templates || [];
  const countsOfTheTemplates = templatesResponse?.total;

  // Update filtered templates when templates data changes (no client-side filtering needed)
  useEffect(() => {
    // All filtering is now done server-side via API
    setFilteredAgents(templates);
  }, [templates]);

  // Handle search for both views
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Handle main tab change (Cards vs Table)
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setSearchQuery(''); // Reset search when switching tabs
    // Reset filters when switching tabs
    if (newValue === 0) {
      // Reset table filters
      setSelectedTypeFilter(0);
      setSelectedCategoryFilter(0);
      setSelectedToolsFilter(0);
      setSelectedModelFilter(0);
      setSelectedStatusFilter(0);
    } else {
      // Reset cards filters
      setSelectedCardsTypeFilter(0);
      setSelectedCardsCategoryFilter(0);
    }
  };

  // Handle cards view filter changes
  const handleCardsTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsTypeFilter(newValue);
  };

  const handleCardsCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsCategoryFilter(newValue);
  };

  // Handle table filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  const handleToolsFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedToolsFilter(newValue);
  };

  const handleModelFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedModelFilter(newValue);
  };

  const handleStatusFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedStatusFilter(newValue);
  };

  return {
    // Data
    templates,
    filteredAgents,

    // Loading states
    isLoading,
    error,
    refetch,
    categoriesLoading,

    // Tab and filter state
    selectedTab,
    searchQuery,

    // Cards view filters
    selectedCardsTypeFilter,
    selectedCardsCategoryFilter,

    // Table view filters
    selectedTypeFilter,
    selectedCategoryFilter,
    selectedToolsFilter,
    selectedModelFilter,
    selectedStatusFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    TOOLS_FILTERS,
    MODEL_FILTERS,
    STATUS_FILTERS,

    // Event handlers
    handleSearch,
    handleTabChange,

    // Cards view filter handlers
    handleCardsTypeFilterChange,
    handleCardsCategoryFilterChange,

    // Table view filter handlers
    handleTypeFilterChange,
    handleCategoryFilterChange,
    handleToolsFilterChange,
    handleModelFilterChange,
    handleStatusFilterChange,
    table,
    countsOfTheTemplates,
  };
};

export type { Template };
