import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import { CONFIG } from 'src/config-global';

import { JwtSignUpView } from 'src/sections/auth/jwt';

// ----------------------------------------------------------------------

export default function Page() {
  const { t } = useTranslation();
  const title = `${t('pages.auth.signUp')} | Jwt - ${CONFIG.site.name}`;

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>

      <JwtSignUpView />
    </>
  );
}
