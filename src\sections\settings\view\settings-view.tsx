import { useState } from 'react';
import { Box, Stack, Divider } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import {
  AppearanceSettings,
  ProfileSettings,
  PasswordSettings,
  DeleteAccountSettings,
} from './index';

// ----------------------------------------------------------------------

export function SettingsView() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('appearance');

  return (
    <Stack direction="row" sx={{ width: 1, height: '100vh' }}>
      <Box
        sx={{ width: '15%', bgcolor: 'background.paper', borderRight: 1, borderColor: 'divider' }}
      >
        <Stack spacing={1} sx={{ p: 2 }}>
          <AppButton
            variant={activeTab === 'profile' ? 'soft' : 'text'}
            startIcon={<Iconify icon="mdi:person-circle-outline" />}
            onClick={() => setActiveTab('profile')}
            fullWidth
            sx={{ justifyContent: 'flex-start' }}
            color='inherit'
            label={t('Profile')}
          />
          <AppButton
            variant={activeTab === 'password' ? 'soft' : 'text'}
            startIcon={<Iconify icon="solar:key-outline" />}
            onClick={() => setActiveTab('password')}
            fullWidth
            sx={{ justifyContent: 'flex-start' }}
            color='inherit'
            label={t('Password')}
          />
          <AppButton
            variant={activeTab === 'appearance' ? 'soft' : 'text'}
            startIcon={<Iconify icon="mdi:palette-outline" />}
            onClick={() => setActiveTab('appearance')}
            fullWidth
            sx={{ justifyContent: 'flex-start' }}
            color='inherit'
            label={t('Appearance')}
          />
          <Divider />
          <AppButton
            variant="text"
            onClick={() => setActiveTab('delete')}
            fullWidth
            startIcon={<Iconify icon="eva:close-fill" />}
            sx={{ color: 'error.main', borderColor: 'error.main', justifyContent: 'flex-start' }}
            label={t('Delete Account')}
          />
        </Stack>
      </Box>

      <Box sx={{ flexGrow: 1, p: 3 }}>
        {activeTab === 'appearance' && <AppearanceSettings />}
        {activeTab === 'profile' && <ProfileSettings />}
        {activeTab === 'password' && <PasswordSettings />}
        {activeTab === 'delete' && <DeleteAccountSettings />}
      </Box>
    </Stack>
  );
}
