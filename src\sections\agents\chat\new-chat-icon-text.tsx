import { Box, Typography } from '@mui/material';
import React from 'react';
import { Iconify } from 'src/components/iconify';

const NewChatIconText = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        textAlign: 'center',
      }}
    >
      <Box
        sx={{
          bgcolor: '#F3F1FC',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 3,
        }}
      >
        <Iconify
          icon="flat-color-icons:flow-chart"
          sx={{ fontSize: 40, color: 'white', width: '80px', height: '80px' }}
        />
      </Box>
      <Typography variant="h4" fontWeight={600} color="#333" sx={{ mb: 1 }}>
        Give your agent a task
      </Typography>
    </Box>
  );
};

export default NewChatIconText;
