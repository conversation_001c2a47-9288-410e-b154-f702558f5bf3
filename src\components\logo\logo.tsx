import type { BoxProps } from '@mui/material/Box';

import { useId, forwardRef } from 'react';

import Box from '@mui/material/Box';
import NoSsr from '@mui/material/NoSsr';
import { useTheme } from '@mui/material/styles';

import { RouterLink } from 'src/routes/components';

import { logoClasses } from './classes';

// ----------------------------------------------------------------------

export type LogoProps = BoxProps & {
  href?: string;
  disableLink?: boolean;
};

export const Logo = forwardRef<HTMLDivElement, LogoProps>(
  ({ width = 40, height = 40, disableLink = false, className, href = '/', sx, ...other }, ref) => {
    const theme = useTheme();

    const gradientId = useId();

    const PRIMARY_LIGHT = theme.vars.palette.primary.light;

    const PRIMARY_MAIN = theme.vars.palette.primary.main;

    const PRIMARY_DARK = theme.vars.palette.primary.dark;

    /*
     * OR using local (public folder)
     * const logo = ( <Box alt="logo" component="img" src={`${CONFIG.site.basePath}/logo/logo-single.svg`} width={width} height={height} /> );
     */

    const logo = (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="34"
        height="32"
        viewBox="0 0 34 32"
        fill="none"
      >
        <path
          d="M23.6755 28.1162C22.754 27.3232 21.3668 27.4327 20.5777 28.3586C20.5727 28.3653 20.5677 28.3704 20.5626 28.3771C17.0963 27.7189 14.6469 25.5539 13.1022 21.7895C13.8142 20.8636 13.6886 19.5319 12.7939 18.7625C11.8724 17.9696 10.4852 18.079 9.69612 19.005C8.90702 19.9309 9.01592 21.3248 9.93738 22.1178C10.6528 22.7323 11.6463 22.803 12.4253 22.372C14.0421 26.1263 16.6339 28.4024 20.1438 29.16C19.9025 29.9714 20.1388 30.8839 20.8207 31.4698C21.7421 32.2627 23.1293 32.1533 23.9184 31.2273C24.7075 30.3014 24.5986 28.9074 23.6772 28.1145L23.6755 28.1162Z"
          fill="url(#paint0_linear_4166_15792)"
        />
        <path
          d="M8.04086 25.4614C8.04086 25.4614 8.02579 25.4597 8.01741 25.458C6.85135 22.1112 7.49302 18.899 9.96588 15.6717C11.1185 15.8283 12.2042 15.0522 12.4203 13.8905C12.6431 12.6919 11.8557 11.5387 10.6628 11.3148C9.46997 11.0908 8.32233 11.8821 8.0995 13.0808C7.92694 14.0101 8.36422 14.9108 9.12316 15.372C6.69721 18.6566 6.02873 22.0489 7.13113 25.4833C6.31187 25.6786 5.64172 26.3402 5.47753 27.2274C5.25471 28.4261 6.04214 29.5793 7.23501 29.8032C8.42788 30.0271 9.57552 29.2359 9.79834 28.0372C10.0212 26.8385 9.23374 25.6853 8.04086 25.4614Z"
          fill="url(#paint1_linear_4166_15792)"
        />
        <path
          d="M13.6701 9.88186C14.1107 10.9644 15.3237 11.5199 16.4328 11.126C17.5771 10.7203 18.1769 9.45761 17.7731 8.30776C17.3694 7.15792 16.1128 6.55521 14.9685 6.96094C14.0806 7.27576 13.5244 8.10574 13.5059 8.99801C9.46325 8.5283 6.20463 9.64448 3.79543 12.3196C3.21742 11.7051 2.31104 11.4526 1.46665 11.7523C0.322367 12.158 -0.277419 13.4206 0.126348 14.5705C0.530114 15.7203 1.78665 16.323 2.93093 15.9173C4.07522 15.5116 4.675 14.2489 4.27124 13.0991C4.26789 13.0923 4.26454 13.0856 4.26286 13.0772C6.56484 10.3903 9.65424 9.34145 13.6718 9.87849L13.6701 9.88186Z"
          fill="url(#paint2_linear_4166_15792)"
        />
        <path
          d="M9.93734 3.88369C10.8588 4.67662 12.246 4.5672 13.0351 3.64126C13.0401 3.63452 13.0452 3.62947 13.0502 3.62274C16.5166 4.281 18.966 6.44601 20.5107 10.2104C19.7986 11.1363 19.9243 12.468 20.8189 13.2373C21.7404 14.0303 23.1276 13.9209 23.9167 12.9949C24.7058 12.069 24.5969 10.675 23.6755 9.88208C22.9601 9.26759 21.9666 9.19689 21.1875 9.62787C19.5708 5.87361 16.979 3.59749 13.469 2.8399C13.7103 2.02844 13.4741 1.11597 12.7922 0.530106C11.8707 -0.262833 10.4835 -0.153404 9.69441 0.772534C8.90531 1.69847 9.01421 3.09243 9.93567 3.88537L9.93734 3.88369Z"
          fill="url(#paint3_linear_4166_15792)"
        />
        <path
          d="M25.5737 6.5384C25.5737 6.5384 25.5888 6.54008 25.5972 6.54176C26.7632 9.88861 26.1216 13.1008 23.6487 16.3281C22.496 16.1715 21.4104 16.9476 21.1943 18.1093C20.9714 19.3079 21.7589 20.4611 22.9517 20.685C24.1446 20.909 25.2923 20.1177 25.5151 18.919C25.6876 17.9897 25.2504 17.089 24.4914 16.6278C26.9174 13.3432 27.5858 9.9509 26.4834 6.51651C27.3027 6.32122 27.9729 5.6596 28.137 4.77238C28.3599 3.57371 27.5724 2.4205 26.3796 2.19659C25.1867 1.97268 24.0391 2.76394 23.8162 3.96261C23.5934 5.16127 24.3808 6.31449 25.5737 6.5384Z"
          fill="url(#paint4_linear_4166_15792)"
        />
        <path
          d="M33.4882 17.4274C33.0844 16.2775 31.8279 15.6748 30.6836 16.0806C29.5393 16.4863 28.9395 17.7489 29.3433 18.8988C29.3466 18.9055 29.35 18.9123 29.3517 18.9207C27.0497 21.6076 23.9603 22.6564 19.9427 22.1194C19.5021 21.0369 18.2891 20.4813 17.18 20.8752C16.0357 21.281 15.436 22.5436 15.8397 23.6935C16.2435 24.8433 17.5 25.446 18.6443 25.0403C19.5323 24.7255 20.0885 23.8955 20.1069 23.0032C24.1496 23.4729 27.4082 22.3567 29.8174 19.6816C30.3954 20.2961 31.3018 20.5486 32.1462 20.249C33.2905 19.8432 33.8903 18.5806 33.4865 17.4308L33.4882 17.4274Z"
          fill="url(#paint5_linear_4166_15792)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_4166_15792"
            x1="16.8064"
            y1="18.2322"
            x2="16.8064"
            y2="32.0034"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4166_15792"
            x1="8.94892"
            y1="11.2777"
            x2="8.94892"
            y2="29.8402"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4166_15792"
            x1="8.9489"
            y1="6.83636"
            x2="8.9489"
            y2="16.0452"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4166_15792"
            x1="-57.4431"
            y1="-0.00357067"
            x2="-57.2739"
            y2="13.7676"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4166_15792"
            x1="24.6657"
            y1="2.15955"
            x2="24.6657"
            y2="20.7221"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4166_15792"
            x1="24.6656"
            y1="15.9543"
            x2="24.6656"
            y2="25.1632"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#7D40D9" />
            <stop offset="1" stopColor="#B5ABEE" />
          </linearGradient>
        </defs>
      </svg>
    );

    return (
      <NoSsr
        fallback={
          <Box
            width={width}
            height={height}
            className={logoClasses.root.concat(className ? ` ${className}` : '')}
            sx={{ flexShrink: 0, display: 'inline-flex', verticalAlign: 'middle', ...sx }}
          />
        }
      >
        <Box
          ref={ref}
          component={RouterLink}
          href={href}
          width={width}
          height={height}
          className={logoClasses.root.concat(className ? ` ${className}` : '')}
          aria-label="logo"
          sx={{
            flexShrink: 0,
            display: 'inline-flex',
            verticalAlign: 'middle',
            ...(disableLink && { pointerEvents: 'none' }),
            ...sx,
          }}
          {...other}
        >
          {logo}
        </Box>
      </NoSsr>
    );
  }
);
