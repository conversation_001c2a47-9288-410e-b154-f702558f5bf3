import { useState, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
// @mui
import FormHelperText from '@mui/material/FormHelperText';
import { Box, Stack, Typography, TextField } from '@mui/material';
import { Iconify } from '../iconify';

// ----------------------------------------------------------------------

type RHFCodesProps = {
  name: string;
  length?: number;
  helperText?: string;
  sx?: Record<string, any>;
};

export default function RHFCode({ name, length = 5, helperText, sx, ...other }: RHFCodesProps) {
  const { control, setValue, watch } = useFormContext();
  const codeValue = watch(name) || '';
  const [individualCodes, setIndividualCodes] = useState<string[]>(Array(length).fill(''));

  // Update individual codes when the main code value changes
  useEffect(() => {
    if (codeValue) {
      const codeArray = codeValue.split('').slice(0, length);
      setIndividualCodes([...codeArray, ...Array(length - codeArray.length).fill('')]);
    } else {
      setIndividualCodes(Array(length).fill(''));
    }
  }, [codeValue, length]);

  // Handle change in individual code fields
  const handleCodeChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d$/.test(value)) {
      return;
    }

    const newCodes = [...individualCodes];
    newCodes[index] = value;
    setIndividualCodes(newCodes);

    // Update the main code value
    setValue(name, newCodes.join(''), { shouldValidate: true });

    // Auto-focus next input when a digit is entered
    if (value && index < length - 1) {
      const nextInput = document.getElementById(`code-input-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ fieldState: { error } }) => (
        <div>
          <Stack direction="row" spacing={2} justifyContent="center" sx={sx}>
            {Array(length)
              .fill('')
              .map((_, index) => (
                <TextField
                  // Using index as key is acceptable here since the array order is fixed
                  key={`code-input-${index}`}
                  id={`code-input-${index}`}
                  inputProps={{
                    maxLength: 1,
                    style: { textAlign: 'center' },
                  }}
                  value={individualCodes[index]}
                  onChange={(e) => handleCodeChange(index, e.target.value)}
                  autoFocus={index === 0}
                  placeholder="-"
                  error={!!error}
                  variant="outlined"
                  sx={{
                    width: 60,
                    height: 90,
                    '& .MuiInputBase-root': {
                      height: '69px',
                    },
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: individualCodes[index] ? 'success.main' : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: individualCodes[index] ? 'success.main' : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: individualCodes[index] ? 'success.main' : 'primary.main',
                      },
                    },
                  }}
                  {...other}
                />
              ))}
          </Stack>

          {error && (
            <FormHelperText sx={{ px: 2, mt: 1 }} error>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify sx={{ mr: 0.5 }} icon="jam:triangle-danger-f" /> {error.message}
              </Box>
            </FormHelperText>
          )}

          {helperText && (
            <Stack display="flex" justifyContent="flex-start">
              <Box color="red" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Iconify sx={{ mr: 0.5 }} icon="jam:triangle-danger-f" />
                <Typography fontSize="14px">{helperText}</Typography>
              </Box>
            </Stack>
          )}
        </div>
      )}
    />
  );
}
