import { Box } from '@mui/material';

export const AuthModal = ({ url, onClose }: { url: string; onClose: () => void }) => (
  <Box
    sx={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      backgroundColor: 'rgba(0, 0, 0, 0.85)',
      zIndex: 1300,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Box
      sx={{
        width: '90%',
        maxWidth: 600,
        height: 600,
        backgroundColor: 'white',
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: 24,
      }}
    >
      <iframe src={url} width="100%" height="100%" style={{ border: 'none' }} title="OAuth" />
    </Box>
    <Box
      sx={{
        position: 'absolute',
        top: 16,
        right: 16,
        cursor: 'pointer',
        color: 'white',
        fontSize: 24,
        fontWeight: 'bold',
      }}
      onClick={onClose}
    >
      ✕
    </Box>
  </Box>
);
