import { useTranslation } from 'react-i18next';
import { useBoolean } from 'src/hooks/use-boolean';
import { AppButton } from '../common/app-button';
import ConfirmDialog from './confirm-dialog';
import { DeleteDialogPropsType } from './types';
import { Iconify } from '../iconify';

export const DeleteDialog = ({
  open,
  onClose,
  handleDelete,
  isDeleting,
}: DeleteDialogPropsType) => {
  const { t } = useTranslation();
  const confirm = useBoolean();

  return (
    <>
      {/* Confirm */}
      <ConfirmDialog
        open={open}
        onClose={(event: React.SyntheticEvent<Element, Event>, reason: string) => {
          if (reason && reason === 'backdropClick') {
            confirm.onFalse();
            onClose();
          }
        }}
        content={t('dialogs.confirmDeleteMessage')}
        icon={
          <Iconify
            icon="material-symbols:warning"
            style={{ color: 'red' }}
            sx={{
              mx: 'auto',
              width: '70px',
              height: '70px',
            }}
          />
        }
        action={
          <>
            <AppButton
              label={t('buttons.delete')}
              isLoading={isDeleting}
              onClick={handleDelete}
              color="error"
              size="large"
            />
            <AppButton
              variant="outlined"
              label={t('buttons.cancel')}
              onClick={() => {
                confirm.onFalse();
                onClose();
              }}
              color="inherit"
              size="large"
            />
          </>
        }
      />
    </>
  );
};

export default DeleteDialog;
