import { useTheme, useMediaQuery } from '@mui/material';
import { useMemo } from 'react';

/**
 * Custom hook for tablet-specific responsive design
 * Provides boolean flags for different tablet breakpoints
 */
export function useTabletResponsive() {
  const theme = useTheme();
  
  // Create custom media queries for tablet devices
  const isTablet = useMediaQuery('(min-width:768px) and (max-width:1199px)');
  const isSmallTablet = useMediaQuery('(min-width:600px) and (max-width:767px)');
  const isLargeTablet = useMediaQuery('(min-width:768px) and (max-width:899px)');
  
  // Standard breakpoints
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'));
  const isMdDown = useMediaQuery(theme.breakpoints.down('md'));
  const isLgDown = useMediaQuery(theme.breakpoints.down('lg'));
  
  // Combined flags
  const isTabletOrMobile = useMediaQuery('(max-width:1199px)');
  const isTabletOrBelow = isTablet || isSmallTablet || isSmDown;
  
  return useMemo(
    () => ({
      isTablet,
      isSmallTablet,
      isLargeTablet,
      isSmDown,
      isMdDown,
      isLgDown,
      isTabletOrMobile,
      isTabletOrBelow,
    }),
    [
      isTablet,
      isSmallTablet,
      isLargeTablet,
      isSmDown,
      isMdDown,
      isLgDown,
      isTabletOrMobile,
      isTabletOrBelow,
    ]
  );
}

export default useTabletResponsive;
