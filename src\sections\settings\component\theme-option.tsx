import { Box, Stack, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

// ----------------------------------------------------------------------

type Theme = 'light' | 'dark' | 'system';

type ThemeOptionProps = {
  theme: Theme;
  isSelected: boolean;
  onClick: () => void;
};

export function ThemeOption({ theme, isSelected, onClick }: ThemeOptionProps) {
  const { t } = useTranslation();

  const renderThemePreview = () => {
    const isLight = theme === 'light';
    const isSystem = theme === 'system';

    const commonStyles = {
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      borderRadius: 'inherit',
    };

    if (isSystem) {
      return (
        <Box sx={{ ...commonStyles, flexDirection: 'row' }}>
          {/* Light part */}
          <Box sx={{ width: '50%', height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ flexShrink: 0, height: 16, bgcolor: '#F4F6F8' }} />
            <Box sx={{ display: 'flex', flexGrow: 1 }}>
              <Box sx={{ width: 24, flexShrink: 0, bgcolor: '#F9FAFB', p: 0.5 }}>
                <Box sx={{ height: 4, bgcolor: 'primary.main', borderRadius: 0.5, opacity: 0.5 }} />
              </Box>
              <Box sx={{ flexGrow: 1, bgcolor: '#FFFFFF', p: 0.5 }}>
                <Box sx={{ height: 4, bgcolor: 'grey.400', borderRadius: 0.5, mb: 0.5 }} />
                <Box sx={{ height: 4, bgcolor: 'grey.400', borderRadius: 0.5, opacity: 0.5 }} />
              </Box>
            </Box>
          </Box>
          {/* Dark part */}
          <Box sx={{ width: '50%', height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ flexShrink: 0, height: 16, bgcolor: '#212B36' }} />
            <Box sx={{ display: 'flex', flexGrow: 1 }}>
              <Box sx={{ width: 24, flexShrink: 0, bgcolor: '#161C24', p: 0.5 }}>
                <Box sx={{ height: 4, bgcolor: 'primary.main', borderRadius: 0.5 }} />
              </Box>
              <Box sx={{ flexGrow: 1, bgcolor: '#212B36', p: 0.5 }}>
                <Box sx={{ height: 4, bgcolor: 'grey.600', borderRadius: 0.5, mb: 0.5 }} />
                <Box sx={{ height: 4, bgcolor: 'grey.600', borderRadius: 0.5, opacity: 0.5 }} />
              </Box>
            </Box>
          </Box>
        </Box>
      );
    }

    const headerBg = isLight ? '#F4F6F8' : '#212B36';
    const sidebarBg = isLight ? '#F9FAFB' : '#161C24';
    const contentBg = isLight ? '#FFFFFF' : '#212B36';
    const accentColor = isLight ? 'grey.400' : 'grey.600';
    const accentOpacity = isLight ? 0.5 : 1;

    return (
      <Box sx={commonStyles}>
        <Box sx={{ flexShrink: 0, height: 16, bgcolor: headerBg }} />
        <Box sx={{ display: 'flex', flexGrow: 1 }}>
          <Box sx={{ width: 24, flexShrink: 0, bgcolor: sidebarBg, p: 0.5 }}>
            <Box
              sx={{ height: 4, bgcolor: 'primary.main', borderRadius: 0.5, opacity: accentOpacity }}
            />
          </Box>
          <Box sx={{ flexGrow: 1, bgcolor: contentBg, p: 0.5 }}>
            <Box sx={{ height: 4, bgcolor: accentColor, borderRadius: 0.5, mb: 0.5 }} />
            <Box sx={{ height: 4, bgcolor: accentColor, borderRadius: 0.5, opacity: 0.5 }} />
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Stack spacing={1} alignItems="flex-start">
      <Box
        onClick={onClick}
        sx={{
          cursor: 'pointer',
          border: '2px solid',
          borderColor: isSelected ? 'primary.main' : 'background.neutral',
          borderRadius: 1.5,
          width: 148,
          height: 100,
          bgcolor: 'background.neutral',
          boxShadow: (th) =>
            isSelected ? `0 0 0 1px ${th.palette.primary.main}` : th.customShadows.card,
          transition: (th) => th.transitions.create(['border-color', 'box-shadow']),
          '&:hover': {
            borderColor: 'primary.light',
          },
        }}
      >
        {renderThemePreview()}
      </Box>
      <Typography variant="caption" sx={{ textTransform: 'capitalize', pl: 0.5 }}>
        {t(theme)}
      </Typography>
    </Stack>
  );
}
