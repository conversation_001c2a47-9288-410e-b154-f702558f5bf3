// @mui
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Icon } from '@mui/material';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import { ConfirmDialogProps } from './types';
import { Iconify } from '../iconify';

// ----------------------------------------------------------------------

export default function ConfirmDialog({
  title,
  content,
  action,
  open,
  onClose,
  close,
  icon,
  ...other
}: ConfirmDialogProps) {
  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={{ pb: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="end">
          {close && (
            <IconButton onClick={close} sx={{ justifySelf: 'end' }}>
              <Iconify icon="material-symbols:cancel-outline" />
            </IconButton>
          )}
        </Stack>
      </DialogTitle>

      {content && (
        <DialogContent>
          <Stack p={1} direction="column" spacing={0.5}>
            {icon}
            {title && title}
            <Typography variant="h4" textAlign="center">
              {content}
            </Typography>
          </Stack>
        </DialogContent>
      )}

      <DialogActions style={{ justifyContent: 'center' }}>{action}</DialogActions>
    </Dialog>
  );
}
