import { createContext, useContext, useState, ReactNode, useMemo } from 'react';

// ----------------------------------------------------------------------

type AccountMenuContextValue = {
  isGlobalMenuVisible: boolean;
  showGlobalMenu: () => void;
  hideGlobalMenu: () => void;
  toggleGlobalMenu: () => void;
};

const initialState: AccountMenuContextValue = {
  isGlobalMenuVisible: true,
  showGlobalMenu: () => {},
  hideGlobalMenu: () => {},
  toggleGlobalMenu: () => {},
};

const AccountMenuContext = createContext<AccountMenuContextValue>(initialState);

type AccountMenuProviderProps = {
  children: ReactNode;
};

export function AccountMenuProvider({ children }: AccountMenuProviderProps) {
  const [isGlobalMenuVisible, setIsGlobalMenuVisible] = useState(true);

  const showGlobalMenu = () => {
    setIsGlobalMenuVisible(true);
  };

  const hideGlobalMenu = () => {
    setIsGlobalMenuVisible(false);
  };

  const toggleGlobalMenu = () => {
    setIsGlobalMenuVisible((prev) => !prev);
  };

  const value = useMemo(
    () => ({
      isGlobalMenuVisible,
      showGlobalMenu,
      hideGlobalMenu,
      toggleGlobalMenu,
    }),
    [isGlobalMenuVisible]
  );

  return (
    <AccountMenuContext.Provider value={value}>
      {children}
    </AccountMenuContext.Provider>
  );
}

export function useAccountMenu() {
  const context = useContext(AccountMenuContext);

  if (!context) {
    throw new Error('useAccountMenu must be used within AccountMenuProvider');
  }

  return context;
}
