import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for TeamTeamplatess
export const teamTeamplatesEndPoints = {
  list: '/templates-teams',
  details: '/templates-teams',
};

// Define the Category interface

// Define the TeamTeamplates data type based on the new API response
export interface TeamTeamplatesTool {
  id: number;
  TeamTeamplatesId: number;
  toolId: number;
  createdAt: string;
  tool: {
    id: number;
    icon: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
}

export type TeamInTemplatesType = {
  id: number;
  createdAt: string;
  template: {
    id: number;
    creatorId: number | null;
    name: string;
    description: string;
    categoryId: number;
    type: 'SINGLE' | 'MULTI' | string;
    model: string;
    status: 'ACTIVE' | 'INACTIVE' | string;
    createdAt: string;
    updatedAt: string;
    systemMessage: string;
    visibility: 'PUBLIC' | 'PRIVATE' | string;
    category: {
      id: number;
      name: string;
      description: string;
      icon: string;
      theme: string;
      createdAt: string;
      updatedAt: string;
    };
    templateTools: {
      id: number;
      templateId: number;
      toolId: number;
      createdAt: string;
      tool: {
        id: number;
        name: string;
        description: string;
        url: string | null;
        fields: unknown | null;
        icon: string;
        createdAt: string;
        updatedAt: string;
      };
    }[];
  };
};

interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeamTeamplatesType {
  id: number;
  createdAt: string;
  updatedAt: string;
  description: string;
  name: string;
  categoryId: number;
  type: 'AUTO' | 'MANUAL' | string;
  visibility: 'PUBLIC' | 'PRIVATE' | string;
  category: Category;
  model: string;
  templatesInTeam: TeamInTemplatesType[];
  status: 'ACTIVE' | 'INACTIVE' | string;
}

export interface TeamTeamplatessResponse {
  templatesTeams: TeamTeamplatesType[];
  total: number;
}

// Define the API response structure

// Define filter parameters interface
export interface TeamTeamplatesFilters {
  visibility?: 'PUBLIC' | 'PRIVATE';
  type?: string;
  category?: string;
  categoryId?: number;
  tools?: string;
  model?: string;
  status?: string;
  name?: string;
  take?: number;
  skip?: number;
}

export type TemplatesTeamsResponse = {
  templatesTeams: TeamTeamplatesType[];
  total: number;
};

// Create a hook to use the TeamTeamplatess API
export const useTeamTeamplatessApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all TeamTeamplatess with optional filters
  const useGetTeamTeamplatess = (filters?: TeamTeamplatesFilters) => {
    return apiServices.useGetListService<TemplatesTeamsResponse, TeamTeamplatesFilters>({
      url: teamTeamplatesEndPoints.list,
      params: filters,
    });
  };

  // Get a single TeamTeamplates by ID
  const useGetTeamTeamplates = (id: string) => {
    return apiServices.useGetItemService<TeamTeamplatesType>({
      url: teamTeamplatesEndPoints.details,
      id,
    });
  };

  // Create a new TeamTeamplates
  const useCreateTeamTeamplates = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<TeamTeamplatesType, any>({
      url: teamTeamplatesEndPoints.list,
      onSuccess,
    });
  };

  // Update a TeamTeamplates
  const useUpdateTeamTeamplates = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<TeamTeamplatesType>({
      url: teamTeamplatesEndPoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a TeamTeamplates
  const useDeleteTeamTeamplates = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: teamTeamplatesEndPoints.details,
      onSuccess,
    });
  };

  return {
    useGetTeamTeamplatess,
    useGetTeamTeamplates,
    useCreateTeamTeamplates,
    useUpdateTeamTeamplates,
    useDeleteTeamTeamplates,
  };
};
