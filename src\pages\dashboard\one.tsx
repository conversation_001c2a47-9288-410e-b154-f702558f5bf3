import { Card } from '@mui/material';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import { CONFIG } from 'src/config-global';

import { OverviewView } from 'src/sections/overview/view/overview-view';

// ----------------------------------------------------------------------

export default function Page() {
  const { t } = useTranslation();
  const title = `${t('pages.dashboard.overView')} | ${t('pages.dashboard.title')} - ${CONFIG.site.name}`;

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>
      <Card sx={{ padding: '24px', mx: '12px' }}>
        <OverviewView />
      </Card>
    </>
  );
}
