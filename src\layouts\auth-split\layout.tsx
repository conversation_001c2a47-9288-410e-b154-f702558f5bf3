import type { Theme, SxProps } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { CONFIG } from 'src/config-global';


export type AuthSplitLayoutProps = {
  sx?: SxProps<Theme>;
  children: React.ReactNode;
};

export function AuthSplitLayout({
  sx,
  children,
}: AuthSplitLayoutProps) {
  return (
    <Box
      sx={{
        backgroundImage: `url(${CONFIG.site.basePath}/assets/background/background.png)`,
  backgroundPosition:'center',
  backgroundRepeat:'no-repeat',
  backgroundSize:'100vw',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        ...sx,
      }}
    >
      {children}
    </Box>
  );
}
