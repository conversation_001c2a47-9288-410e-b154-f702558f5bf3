import {
  Alert,
  Box,
  Paper,
  Typography,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  Stack,
  Divider,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { enqueueSnackbar } from 'notistack';
import { Iconify } from 'src/components/iconify';
import { StreamedMessage } from './use-agents-chat';

type ChatMessagesType = {
  chatMessages: StreamedMessage[];
  message?: string;
  name: string;
};

const ChatMessagesEvents = ({ chatMessages, message, name }: ChatMessagesType) => {
  const currentLng = localStorage.getItem('i18nextLng') || 'en';
  const errorMsg = chatMessages?.find((msg) => msg.event === 'error');
  const [expandedAccordion, setExpandedAccordion] = useState<number | null>(null);

  const handleAccordionChange = (messageId: number) => {
    setExpandedAccordion(expandedAccordion === messageId ? null : messageId);
  };

  useEffect(() => {
    if (errorMsg) {
      enqueueSnackbar({
        variant: 'error',
        message: errorMsg?.message,
        anchorOrigin: {
          vertical: 'top',
          horizontal: currentLng === 'en' ? 'right' : 'left',
        },
      });
    }
  }, [chatMessages]);

  console.log('Message', message);

  // Group messages by conversation flow
  const groupedMessages = chatMessages.reduce(
    (acc, msg, index) => {
      if (msg.source === 'user') {
        // Start a new conversation group
        acc.push({
          userMessage: msg,
          responses: [],
          id: msg.id,
        });
      } else if (acc.length > 0) {
        // Add to the latest conversation group
        acc[acc.length - 1].responses.push(msg);
      }
      return acc;
    },
    [] as Array<{ userMessage: StreamedMessage; responses: StreamedMessage[]; id: number }>
  );

  // Auto-expand the latest accordion when new messages arrive
  useEffect(() => {
    if (groupedMessages.length > 0) {
      // Expand the latest conversation group
      const latestGroupId = groupedMessages[groupedMessages.length - 1].id;
      setExpandedAccordion(latestGroupId);
    } else if (chatMessages.length > 0) {
      // Expand the ungrouped messages accordion
      setExpandedAccordion(-1);
    }
  }, [chatMessages.length, groupedMessages.length]);

  return (
    <>
      {message && message.length > 0 && (
        <Alert sx={{ mt: '20px' }} variant="filled" color="error">
          <Typography sx={{ color: 'white' }}>{message}</Typography>
        </Alert>
      )}

      <Stack spacing={3} sx={{ mt: 2 }}>
        {groupedMessages.length === 0 && chatMessages.length > 0 && (
          // Handle messages that don't start with user message

          <Accordion
            expanded={expandedAccordion === -1}
            onChange={() => handleAccordionChange(-1)}
            sx={{
              boxShadow: 'none',
              border: '1px solid #E0E0E0',
              borderRadius: '12px !important',
              backgroundColor: 'white',
              '&:before': {
                display: 'none',
              },
              '&.Mui-expanded': {
                margin: 0,
              },
            }}
          >
            <AccordionSummary
              expandIcon={<Iconify icon="eva:chevron-down-fill" />}
              sx={{
                backgroundColor: 'white',
                borderRadius: '12px',
                minHeight: '48px',
                '&.Mui-expanded': {
                  borderBottomLeftRadius: 0,
                  borderBottomRightRadius: 0,
                },
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center',
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    backgroundColor: 'white',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 'bold',
                  }}
                >
                  A
                </Avatar>
                <Typography variant="body2" fontWeight={500}>
                  Agent Response
                </Typography>
              </Box>
              <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip size="small" color="success" label="Completed" />

                <Typography variant="caption" color="text.disabled">
                  Now
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ backgroundColor: 'white', borderRadius: '0 0 12px 12px' }}>
              <Stack spacing={2}>
                {chatMessages.map((msg) => (
                  <Box key={msg.id}>
                    <Typography variant="body2" whiteSpace="pre-wrap" sx={{ mb: 1 }}>
                      {msg.message}
                    </Typography>
                    {msg.status === 'completed' && (
                      <Chip size="small" color="success" label="Completed" />
                    )}
                  </Box>
                ))}
              </Stack>
            </AccordionDetails>
          </Accordion>
        )}

        {groupedMessages.map((group) => (
          <Box key={group.id}>
            {/* User Message Bubble */}
            <Box sx={{ display: 'flex', justifyContent: 'right', mb: 2 }}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: '#EFEBF8',
                  border: '1px solid #E0E0E0',
                  borderRadius: '12px',
                  boxShadow: 'none',
                  width: 'fit-content',
                  maxWidth: '80%',
                  textAlign: 'right',
                }}
              >
                <Typography variant="body1" fontWeight={500}>
                  {group.userMessage.message}
                </Typography>
                <Typography
                  variant="caption"
                  color="text.disabled"
                  sx={{ mt: 0.5, display: 'block' }}
                >
                  Now
                </Typography>
              </Paper>
            </Box>

            {/* Single Accordion for All Agent Responses */}
            <Accordion
              expanded={expandedAccordion === group.id}
              onChange={() => handleAccordionChange(group.id)}
              sx={{
                boxShadow: 'none',
                border: '1px solid #E0E0E0',
                borderRadius: '12px !important',
                backgroundColor: 'white',
                '&:before': {
                  display: 'none',
                },
                '&.Mui-expanded': {
                  margin: 0,
                },
              }}
            >
              <AccordionSummary
                expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                sx={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  minHeight: '48px',
                  '&.Mui-expanded': {
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                  },
                  '& .MuiAccordionSummary-content': {
                    alignItems: 'center',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: '#9C6FE4',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: 'bold',
                    }}
                  >
                    A
                  </Avatar>
                  <Typography variant="body2" fontWeight={500} sx={{ flex: 1 }}>
                    {name || 'Task completed'}
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails
                sx={{ backgroundColor: 'white', borderRadius: '0 0 12px 12px', p: 2 }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
                  {group.responses.some((msg) => msg.status === 'completed') && (
                    <Chip size="small" color="success" label="Completed" variant="soft" />
                  )}
                  {group.responses.some((msg) => msg.isError) && (
                    <Chip size="small" color="error" label="Failed" variant="soft" />
                  )}
                  <Typography variant="caption" color="text.disabled">
                    Now
                  </Typography>
                </Box>
                <Stack spacing={2}>
                  {/* Agent Responses */}
                  {group.responses.map((msg) => (
                    <Box key={msg.id}>
                      {/* Response Header */}
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1, mt: '24px' }}
                      >
                        <Typography variant="body2" fontWeight={500} color="text.secondary">
                          {msg.source === 'Tool'
                            ? msg.event === 'tool_call_request'
                              ? 'Tool Request'
                              : msg.event === 'tool_call_response'
                                ? 'Tool Response'
                                : 'Tool'
                            : msg.event === 'task_result'
                              ? 'Task Result'
                              : msg.event === 'complete'
                                ? 'Task Completed'
                                : msg.event === 'error'
                                  ? 'Task Failed'
                                  : msg.source}
                        </Typography>
                        <Typography variant="caption" color="text.disabled">
                          Now
                        </Typography>
                        {msg.status === 'completed' && (
                          <Chip size="small" color="success" label="Completed" />
                        )}
                        {msg.isError && <Chip size="small" color="error" label="Failed" />}
                      </Box>

                      {/* Response Content */}
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: '#F8F9FA',
                          border: '1px solid #E0E0E0',
                          borderRadius: 2,
                          boxShadow: 'none',
                        }}
                      >
                        {msg?.event?.startsWith('tool_call') ? (
                          <Box>
                            {msg.message.split('\n\n').map((block, idx) => (
                              <Box
                                key={idx}
                                sx={{ mb: idx < msg.message.split('\n\n').length - 1 ? 2 : 0 }}
                              >
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Iconify icon="mdi:google" width={16} height={16} />
                                  <Typography variant="body2" fontWeight={500}>
                                    {block.split('\n')[0]}
                                  </Typography>
                                  {msg.event === 'tool_call_response' && (
                                    <Box sx={{ ml: 'auto' }}>
                                      {msg.isError ? (
                                        <Iconify
                                          icon="dashicons:no"
                                          style={{
                                            color: '#ff473e',
                                            width: '16px',
                                            height: '16px',
                                          }}
                                        />
                                      ) : (
                                        <Iconify
                                          icon="el:ok"
                                          style={{
                                            color: '#167548',
                                            width: '16px',
                                            height: '16px',
                                          }}
                                        />
                                      )}
                                    </Box>
                                  )}
                                </Box>
                                {block
                                  .split('\n')
                                  .slice(1)
                                  .map((line, i) => (
                                    <Typography
                                      key={i}
                                      variant="body2"
                                      whiteSpace="pre-wrap"
                                      sx={{ mt: 1 }}
                                    >
                                      {line}
                                    </Typography>
                                  ))}
                              </Box>
                            ))}
                          </Box>
                        ) : (
                          <Box>
                            <Typography variant="body2" whiteSpace="pre-wrap" sx={{ mb: 1 }}>
                              {msg.event === 'error'
                                ? msg.message || 'An error occurred'
                                : msg.message}
                            </Typography>

                            {/* Task Complete */}
                            {msg.event === 'complete' && !errorMsg && (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                  p: 1.5,
                                  backgroundColor: '#E8F5E8',
                                  borderRadius: 1,
                                  border: '1px solid #4CAF50',
                                  mt: 1,
                                }}
                              >
                                <Iconify
                                  icon="el:ok"
                                  style={{ color: '#167548', width: '16px', height: '16px' }}
                                />
                                <Typography
                                  sx={{ color: '#167548' }}
                                  variant="body2"
                                  fontWeight={500}
                                >
                                  Task Completed Successfully
                                </Typography>
                              </Box>
                            )}

                            {/* Task Error */}
                            {msg.event === 'error' && (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                  p: 1.5,
                                  backgroundColor: '#FFEBEE',
                                  borderRadius: 1,
                                  border: '1px solid #F44336',
                                  mt: 1,
                                }}
                              >
                                <Iconify
                                  icon="dashicons:no"
                                  style={{ color: '#ff473e', width: '16px', height: '16px' }}
                                />
                                <Typography
                                  sx={{ color: '#ff473e' }}
                                  variant="body2"
                                  fontWeight={500}
                                >
                                  Task Failed
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        )}
                      </Paper>
                    </Box>
                  ))}
                </Stack>
              </AccordionDetails>
            </Accordion>
          </Box>
        ))}
      </Stack>
    </>
  );
};

export default ChatMessagesEvents;
