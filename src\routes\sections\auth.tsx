import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { AuthSplitLayout } from 'src/layouts/auth-split';

import { SplashScreen } from 'src/components/loading-screen';

import { GuestGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

/** **************************************
 * Jwt
 *************************************** */
const Jwt = {
  SignInPage: lazy(() => import('src/pages/auth/jwt/sign-in')),
  SignUpPage: lazy(() => import('src/pages/auth/jwt/sign-up')),
  ForgotPasswordPage: lazy(() => import('src/pages/auth/jwt/forgot-password')),
  NewPasswordPage: lazy(() => import('src/pages/auth/jwt/new-password')),
  OauthCallbackPage: lazy(() => import('src/pages/auth/jwt/oauth-callback')),
  JwtVerifyEmailpage: lazy(() => import('src/pages/auth/jwt/verify-email')),
};

const authJwt = {
  path: 'jwt',
  children: [
    {
      path: 'sign-in',
      element: (
        <GuestGuard>
          <AuthSplitLayout>
            <Jwt.SignInPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'sign-up',
      element: (
        <GuestGuard>
          <AuthSplitLayout>
            <Jwt.SignUpPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'forgot-password',
      element: (
        <GuestGuard>
          <AuthSplitLayout>
            <Jwt.ForgotPasswordPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'new-password',
      element: (
        <GuestGuard>
          <AuthSplitLayout>
            <Jwt.NewPasswordPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'verify-email',
      element: (
        <GuestGuard>
          {/* <AuthSplitLayout> */}
          <Jwt.JwtVerifyEmailpage />
          {/* </AuthSplitLayout> */}
        </GuestGuard>
      ),
    },
  ],
};

// ----------------------------------------------------------------------

export const authRoutes = [
  {
    path: 'auth',
    element: (
      <Suspense fallback={<SplashScreen />}>
        <Outlet />
      </Suspense>
    ),
    children: [authJwt],
  },
  {
    path: 'oauth/callback',
    element: (
      <GuestGuard>
        <AuthSplitLayout>
          <Jwt.OauthCallbackPage />
        </AuthSplitLayout>
      </GuestGuard>
    ),
  },
];
