import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { KnowledgeBaseView } from 'src/sections/knowledge-base/view';

// ----------------------------------------------------------------------

export default function KnowledgeBasePage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{t('pages.profile.knowledgeBase')}</title>
      </Helmet>

      <KnowledgeBaseView />
    </>
  );
}
