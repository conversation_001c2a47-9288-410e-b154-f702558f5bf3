import {
  MutationKey,
  UseInfiniteQueryOptions,
  UseMutationOptions,
  UseQueryOptions,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import { serialize } from 'object-to-formdata';

import { useApiResult } from './use-api-result';
import {
  IDeleteServiceParams,
  IGetItemServiceParams,
  IGetListServiceParams,
  IPatchServiceParams,
  IPostServiceParams,
  IPutServiceParams,
  InfiniteScrollResType,
  Page,
} from '../types/api-services';

export const useApiServices = ({ axiosInstance }: { axiosInstance: AxiosInstance }) => {
  const { handleApiSuccessWithSnackbar, handleApiErrorWithSnackbar } = useApiResult();

  // GET LIST API SERVICE
  const useGetListService = <DataType, ParamsType = undefined>({
    url,
    params,
    queryOptions,
  }: IGetListServiceParams<ParamsType> & {
    queryOptions?: Partial<UseQueryOptions<DataType>>;
  }) => {
    return useQuery<DataType>({
      queryKey: [url + 'list', params],
      queryFn: async () => {
        const response: AxiosResponse<DataType> = await axiosInstance.get(url, {
          params,
        });
        return response.data;
      },
      ...queryOptions,
    });
  };

  const useGetInfiniteListService = <DataType, ParamsType = undefined>({
    url,
    params,
    queryOptions,
  }: IGetListServiceParams<ParamsType> & {
    queryOptions?: Omit<UseInfiniteQueryOptions<InfiniteScrollResType<DataType>>, 'queryKey'>;
  }) => {
    return useInfiniteQuery({
      queryKey: [url + 'infinite list', params],

      queryFn: async ({ pageParam }) => {
        const response = await axiosInstance.get(url, {
          params: {
            ...params,
            page: pageParam,
          },
        });
        return response.data;
      },

      ...queryOptions,

      getNextPageParam: (lastPage) =>
        lastPage ? (lastPage as unknown as Page<DataType>)?.meta?.next : undefined,
      initialPageParam: 1,
    });
  };

  // GET ITEM API SERVICE
  const useGetItemService = <DataType>({
    url,
    id,
    queryOptions,
  }: IGetItemServiceParams & {
    queryOptions?: Omit<UseQueryOptions<DataType>, 'queryKey'>;
  }) => {
    return useQuery<DataType>({
      queryKey: [url + id + 'item'],
      queryFn: async () => {
        const response: AxiosResponse<DataType> = await axiosInstance.get(`${url}/${id ? id : ''}`);
        return response.data;
      },
      ...queryOptions,
    });
  };

  // POST API SERVICE
  const usePostService = <TVars, TData>({
    url,
    withFormData = false,
    withSnackbar = true,
    onSuccess,
    queryOptions,
  }: IPostServiceParams & {
    queryOptions?: UseMutationOptions<TData, AxiosError, TVars, MutationKey>;
  }) => {
    // const queryClient = useQueryClient();

    return useMutation<TData, AxiosError, TVars, MutationKey>({
      mutationFn: (payload) => {
        return axiosInstance.post(
          url,
          withFormData
            ? serialize(payload, {
                indices: false,
                noFilesWithArrayNotation: true,
              })
            : payload
        );
      },
      onSuccess(data: TData) {
        if (withSnackbar) {
          handleApiSuccessWithSnackbar();
        }
        if (onSuccess) {
          onSuccess(data as TData);
        }
      },
      onError(error) {
        handleApiErrorWithSnackbar(error);
      },
      ...queryOptions,
    });
  };

  // PUT API SERVICE
  const usePutService = <T>({
    url,
    id,
    withFormData = true,
    onSuccess,
    queryOptions,
  }: IPutServiceParams & {
    queryOptions?: UseMutationOptions<AxiosResponse<T>, AxiosError, T, MutationKey>;
  }) => {
    // const queryClient = useQueryClient();

    return useMutation<AxiosResponse<T>, AxiosError, T, MutationKey>({
      mutationFn: (payload) => {
        return axiosInstance.put(
          `${url}/${id}`,
          withFormData ? serialize(payload, { indices: true }) : payload
        );
      },
      onSuccess() {
        handleApiSuccessWithSnackbar();
        if (onSuccess) {
          onSuccess();
        }
      },
      onError(error) {
        handleApiErrorWithSnackbar(error);
      },
      ...queryOptions,
    });
  };

  // PATCH API SERVICE
  const usePatchService = <T>({
    url,
    id,
    withFormData = false,
    onSuccess,
    queryOptions,
    queryKey,
  }: IPatchServiceParams & {
    queryOptions?: UseMutationOptions<AxiosResponse<T>, AxiosError, T, MutationKey>;
  }) => {
    const queryClient = useQueryClient();

    return useMutation<AxiosResponse<T>, AxiosError, T, MutationKey>({
      mutationFn: (payload) => {
        return axiosInstance.patch(
          `${url}/${id ? id : ''}`,
          withFormData ? serialize(payload) : payload
        );
      },
      onSuccess() {
        if (queryKey) {
          queryClient.invalidateQueries({ queryKey: [queryKey] });
        }
        handleApiSuccessWithSnackbar();
        if (onSuccess) {
          onSuccess();
        }
      },
      onError(error) {
        handleApiErrorWithSnackbar(error);
      },
      ...queryOptions,
    });
  };

  // DELETE API SERVICE
  const useDeleteService = <TResponse, TVariables = void>({
    url,
    urlAfterSuccess,
    onSuccess,
    queryOptions,
  }: IDeleteServiceParams & {
    queryOptions?: UseMutationOptions<AxiosResponse<TResponse>, AxiosError, TVariables, MutationKey>;
  }) => {
    const queryClient = useQueryClient();

    return useMutation<AxiosResponse<TResponse>, AxiosError, TVariables, MutationKey>({
      mutationFn: (id) => {
        const endpoint = id ? `${url}/${id}` : url;
        return axiosInstance.delete(endpoint);
      },
      onSuccess() {
        handleApiSuccessWithSnackbar();
        queryClient.invalidateQueries({
          queryKey: [urlAfterSuccess ?? url + 'list'],
        });
        if (onSuccess) {
          onSuccess();
        }
      },
      onError(error) {
        handleApiErrorWithSnackbar(error);
      },
      ...queryOptions,
    });
  };

  return {
    useGetListService,
    useGetItemService,
    usePostService,
    usePutService,
    usePatchService,
    useDeleteService,
    useGetInfiniteListService,
  };
};

export default useApiServices;
