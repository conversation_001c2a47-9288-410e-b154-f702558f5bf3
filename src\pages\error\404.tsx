import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import { CONFIG } from 'src/config-global';

import { NotFoundView } from 'src/sections/error';

// ----------------------------------------------------------------------

export default function Page() {
  const { t } = useTranslation();
  const title = `${t('pages.error.notFoundTitle')} | Error - ${CONFIG.site.name}`;

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>

      <NotFoundView />
    </>
  );
}
