import { Controller, useFormContext } from 'react-hook-form';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import type { StandardTextFieldProps } from '@mui/material/TextField';

// ----------------------------------------------------------------------

type OptionType = {
  value: string | number;
  label: string;
};

type Props = Omit<StandardTextFieldProps, 'select'> & {
  name: string;
  options?: OptionType[];
  children?: React.ReactNode;
};

export function RHFSelectField({ name, label, helperText, options, children, ...other }: Props) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          select
          fullWidth
          label={label}
          error={!!error}
          helperText={error?.message ?? helperText}
          {...other}
        >
          {options
            ? options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))
            : children}
        </TextField>
      )}
    />
  );
}
