import { UseMutationOptions, useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosInstance } from 'axios';
import { useApiResult } from './use-api-result';

interface IUploadFileServiceParams {
  // Endpoint to get the S3 upload URL (POST request)
  presignedUrlEndpoint?: string;
  // Endpoint to get the download URL (GET request)
  downloadUrlEndpoint?: string;
  // Additional parameters for the POST request
  params?: Record<string, any>;
  onSuccess?: (fileUrl: string) => void;
  onStatusChange?: (status: string) => void;
  withSnackbar?: boolean;
  queryOptions?: UseMutationOptions<
    string, // Return type (file URL)
    AxiosError,
    File, // Input type (File object)
    string[]
  >;
}

export const useS3UploadService = ({ axiosInstance }: { axiosInstance: AxiosInstance }) => {
  const { handleApiSuccessWithSnackbar, handleApiErrorWithSnackbar } = useApiResult();

  const useUploadFile = ({
    presignedUrlEndpoint = '/users/generate-profile-upload-url',
    downloadUrlEndpoint = '/files/download-url',
    params = {},
    onSuccess,
    onStatusChange,
    withSnackbar = true,
    queryOptions,
  }: IUploadFileServiceParams) => {
    return useMutation<string, AxiosError, File, string[]>({
      mutationFn: async (file) => {
        if (onStatusChange) onStatusChange('Getting pre-signed URL...');

        const presignedUrlResponse = await axiosInstance.post(presignedUrlEndpoint, {
          fileName: file.name,
          contentType: file.type,
          contentLength: file.size,
        });

        const { url: s3UploadUrl, file: fileInfo } = presignedUrlResponse.data;

        if (onStatusChange) onStatusChange('Uploading file to S3...');

        const s3Response = await fetch(s3UploadUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type,
          },
          credentials: 'omit',
        });

        if (!s3Response.ok) {
          const errorText = await s3Response.text();
          console.error('S3 upload failed:', s3Response.status, errorText);
          throw new Error(`S3 upload failed with status ${s3Response.status}`);
        }

        if (onStatusChange) onStatusChange('Generating download URL...');
        const downloadApiUrl = `/files/download-url?key=${fileInfo.key}`;
        const downloadUrlResponse = await axiosInstance.get(downloadApiUrl);

        return downloadUrlResponse.data.url;
      },
      onSuccess: (fileUrl) => {
        if (withSnackbar) {
          handleApiSuccessWithSnackbar();
        }
        if (onStatusChange) onStatusChange('File uploaded successfully!');
        if (onSuccess) {
          onSuccess(fileUrl);
        }
      },
      onError: (error) => {
        handleApiErrorWithSnackbar(error);
        if (onStatusChange) {
          onStatusChange(`Error: ${error.message || 'Unknown error'}`);
        }
      },
      ...queryOptions,
    });
  };

  return { useUploadFile };
};
