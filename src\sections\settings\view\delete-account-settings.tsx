import { useState } from 'react';
import {
  Typography,
  Stack,
  Box,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { AppButton } from 'src/components/common';
import { useProfileView } from './use-profile-view';

// ----------------------------------------------------------------------

export function DeleteAccountSettings() {
  const { t } = useTranslation();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  const {
    userProfile,
    isDeletingAccount,
    deleteError,
    handleDeleteAccount,
  } = useProfileView();

  const handleDeleteClick = () => {
    setShowConfirmDialog(true);
  };

  const handleConfirmDelete = () => {
    if (confirmationText === 'DELETE') {
      handleDeleteAccount();
      setShowConfirmDialog(false);
    }
  };

  const handleCancelDelete = () => {
    setShowConfirmDialog(false);
    setConfirmationText('');
  };

  return (
    <Stack spacing={3}>
      <Typography variant="h3" color="error.main">
        {t('Delete Account')}
      </Typography>

      {/* Show delete error if any */}
      {deleteError && (
        <Alert severity="error">
          Error deleting account: {deleteError.message}
        </Alert>
      )}

      <Box>
        <Typography variant="body1" sx={{ mb: 2 }}>
          {t('Once you delete your account, there is no going back. Please be certain.')}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {t('This action will permanently delete your account and all associated data. This cannot be undone.')}
        </Typography>

        <AppButton
          variant="outlined"
          color="error"
          onClick={handleDeleteClick}
          disabled={isDeletingAccount}
          sx={{
            borderColor: 'error.main',
            color: 'error.main',
            '&:hover': {
              borderColor: 'error.dark',
              backgroundColor: 'error.main',
              color: 'white',
            },
          }}
          label={isDeletingAccount ? 'Deleting...' : t('Delete Account')}
        />
      </Box>

      {/* Confirmation Dialog */}
      <Dialog
        open={showConfirmDialog}
        onClose={handleCancelDelete}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle color="error.main">
          {t('Confirm Account Deletion')}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {t('Are you sure you want to delete your account? This action cannot be undone.')}
          </Typography>

          <Typography variant="body2" sx={{ mb: 2 }}>
            {t('Please type "DELETE" to confirm:')}
          </Typography>

          <TextField
            fullWidth
            value={confirmationText}
            onChange={(e) => setConfirmationText(e.target.value)}
            placeholder="DELETE"
            variant="outlined"
            size="small"
          />
        </DialogContent>
        <DialogActions>
          <AppButton
            variant="outlined"
            onClick={handleCancelDelete}
            label={t('Cancel')}
          />
          <AppButton
            variant="contained"
            color="error"
            onClick={handleConfirmDelete}
            disabled={confirmationText !== 'DELETE'}
            label={t('Delete Account')}
          />
        </DialogActions>
      </Dialog>
    </Stack>
  );
}
