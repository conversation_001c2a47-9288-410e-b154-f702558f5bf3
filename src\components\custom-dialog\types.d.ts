// @mui
import { DialogProps } from '@mui/material/Dialog';

// ----------------------------------------------------------------------

export type ConfirmDialogProps = Omit<DialogProps, 'title' | 'content'> & {
  close?: any;
  title?: React.ReactNode;
  content: React.ReactNode;
  action?: React.ReactNode;
  onClose?: any;
  icon?: React.ReactNode;
};

export interface DialogPropsType {
  open: boolean;
  onClose: VoidFunction;
}

export interface DeleteDialogPropsType {
  open: boolean;
  onClose: VoidFunction;
  isDeleting: boolean;
  handleDelete: () => void;
}
