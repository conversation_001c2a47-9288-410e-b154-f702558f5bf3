import React, { useEffect } from 'react';
import { signInWithPassword, verifyEmail } from 'src/auth/context/jwt';
import { useAuthContext } from 'src/auth/hooks';
import { LoadingScreen } from 'src/components/loading-screen';
import { useRouter, useSearchParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

const JwtVerifyEmailView = () => {
  const searchParams = useSearchParams();
  const { checkUserSession } = useAuthContext();
  const email = searchParams.get('email');
  const token = searchParams.get('token');
  const router = useRouter();

  const verifyEmailFunc = async () => {
    try {
      // Show success message
      await verifyEmail(email as string, token as string);

      // Redirect to sign in page after a short delay

      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      router.push(paths.auth.jwt.signUp);
      console.error(error);
    }
  };
  useEffect(() => {
    verifyEmailFunc?.();
  }, []);
  return <LoadingScreen />;
};

export default JwtVerifyEmailView;
