import { useEffect, useState } from 'react';

import { Button } from '@mui/material';
import { useRouter } from 'src/hooks/use-router';
import { useSearchParams } from 'src/routes/hooks';
import { localStorageGetItem, localStorageRemoveItem } from 'src/utils/storage-available';
import baseApi from 'src/utils/axios';
import { exchangeOAuthCode } from 'src/auth/context/jwt';
import { ErrorScreen } from 'src/components/error-screen';
import { SplashScreen } from 'src/components/loading-screen';
import { paths } from 'src/routes/paths';

export interface OauthCallbackPageProps {}

export const OauthCallbackPage: React.FC<OauthCallbackPageProps> = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isError, setError] = useState(false);

  useEffect(() => {
    const state = searchParams.get('state');
    const code = searchParams.get('code');
    console.log('state', state);
    console.log('code', code);
    const storedState = localStorageGetItem('oauth_state');
    console.log('storedState', storedState);
    if (!code || !state || state !== storedState) {
      setError(true);
      return;
    }

    (async () => {
      try {
        const { accessToken } = await exchangeOAuthCode(code, state);
        localStorageRemoveItem('oauth_state');
        // const user = await getAuthenticatedUser();

        baseApi.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
        router.push(paths.dashboard.root);
      } catch {
        setError(true);
      }
    })();
  }, []);

  return isError ? (
    <ErrorScreen
      title="Invalid login attempt"
      message="Something went wrong while trying to log you in. Please try again by clicking the button below."
      action={
        <Button
          component="a"
          href={paths.auth.jwt.signIn}
          color="primary"
          size="large"
          variant="contained"
        >
          Retry
        </Button>
      }
    />
  ) : (
    <SplashScreen />
  );
};

export default OauthCallbackPage;
