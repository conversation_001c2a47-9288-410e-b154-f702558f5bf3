import type { ButtonProps } from '@mui/material/Button';
import type { Theme, SxProps } from '@mui/material/styles';

import { useCallback } from 'react';

import Button from '@mui/material/Button';
import { useTranslation } from 'react-i18next';

import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

import { useAuthContext } from 'src/auth/hooks';
import { signOut } from 'src/auth/context/jwt/action';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { Box } from '@mui/material';

// ----------------------------------------------------------------------

type Props = ButtonProps & {
  sx?: SxProps<Theme>;
  onClose?: () => void;
};

export function SignOutButton({ onClose, ...other }: Props) {
  const router = useRouter();
  const { t } = useTranslation();

  const { checkUserSession } = useAuthContext();

  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();

      onClose?.();
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      console.error(error);
    }
  }, [checkUserSession, onClose, router]);

  return (
    <Box >
          <AppButton
            label={t('components.accountMenu.logout')}
            variant="outlined"
            color="primary"
            startIcon={<Iconify icon="eva:log-out-fill" />}
            onClick={handleLogout}
          />
        </Box>
  );
}
