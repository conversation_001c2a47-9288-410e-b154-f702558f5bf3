import { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  LinearProgress,
  Chip,
  TextField,
  InputAdornment,
  IconButton,

  MenuItem,Stack,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { Iconify } from 'src/components/iconify';
import { useTranslation } from 'react-i18next';
import { AppButton } from 'src/components/common';

import {
  ConnectResourceDialog,
  DatabasesDialog,
  APIsDialog,
  KnowledgeBaseTable,
  CloudPlatformsCarousel,
  FileUploadDialog,
} from '../component';
import { useKnowledgeBaseTable } from '../hooks';

// ----------------------------------------------------------------------

// Mock data for cloud storage platforms
const CLOUD_PLATFORMS = [
  {
    id: 'google-drive',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    icon: '/assets/icons/platforms/ic_dropbox.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 50,
  },
  {
    id: 'one-drive',
    name: 'One Drive',
    icon: '/assets/icons/platforms/ic_onedrive.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 30,
  },
  {
    id: 'google-drive-2',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive-2',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    icon: '/assets/icons/platforms/ic_dropbox.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 50,
  },
];

// Mock data for connection options
const CONNECTION_OPTIONS = [
  {
    id: 'connect',
    title: 'Connect',
    description: 'Connect to a new cloud platform',
    icon: 'solar:link-outline',
    color: 'primary.main',
  },
  {
    id: 'databases',
    title: 'Databases',
    description: 'Connect to database',
    icon: 'solar:database-outline',
    color: 'primary.main',
  },
  {
    id: 'api',
    title: 'API',
    description: 'Connect to a custom API',
    icon: 'solar:code-square-outline',
    color: 'primary.main',
  },
];

// ----------------------------------------------------------------------

export function KnowledgeBaseView() {
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [databasesDialogOpen, setDatabasesDialogOpen] = useState(false);
  const [apisDialogOpen, setApisDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const { t } = useTranslation();
  const [fileType, setFileType] = useState('');
  const value = 75;

  const {
    searchQuery,
    setSearchQuery,
    selectedDate,
    setSelectedDate,
    selectedCategory,
    handleClearCategory,
  } = useKnowledgeBaseTable();

  const handleOpenConnectDialog = () => {
    setConnectDialogOpen(true);
  };

  const handleCloseConnectDialog = () => {
    setConnectDialogOpen(false);
  };

  const handleOpenDatabasesDialog = () => {
    setDatabasesDialogOpen(true);
  };

  const handleCloseDatabasesDialog = () => {
    setDatabasesDialogOpen(false);
  };

  const handleOpenApisDialog = () => {
    setApisDialogOpen(true);
  };

  const handleCloseApisDialog = () => {
    setApisDialogOpen(false);
  };

  const handleOpenUploadDialog = () => {
    setUploadDialogOpen(true);
  };

  const handleCloseUploadDialog = () => {
    setUploadDialogOpen(false);
  };

  // Upload Button - opens dialog
  const UploadButton = () => {
    return (
      <AppButton
        variant="contained"
        startIcon={<Iconify icon="eva:upload-fill" />}
        onClick={handleOpenUploadDialog}
        label="Upload"
        sx={{
          bgcolor: 'primary.main',
          color: 'primary.contrastText',
          '&:hover': {
            bgcolor: 'primary.dark',
          },
          borderRadius: 1,
          alignSelf: { xs: 'flex-start', sm: 'center' },
          whiteSpace: 'nowrap',
          px: { xs: 2, sm: 3 },
          py: { xs: 0.75, sm: 1 },
          fontSize: { xs: '0.8125rem', sm: '0.875rem' },
          minWidth: 120,
        }}
      />
    );
  };

  // Connect to Cloud Button with progress indicator
  const ConnectToCloudButton = () => {
    return (
      <AppButton
        variant="outlined"
        startIcon={<Iconify icon="solar:link-outline" />}
        onClick={handleOpenConnectDialog}
        label='Connect to a cloud'
        sx={{
          bgcolor: 'transparent',
          color: 'primary.main',
          borderColor: 'primary.main',
          '&:hover': {
            bgcolor: (theme: any) => theme.palette.primary.main + '08',
            borderColor: 'primary.main'
          },
          borderRadius: 1,
          alignSelf: { xs: 'flex-start', sm: 'center' },
          whiteSpace: 'nowrap',
          px: { xs: 2, sm: 4 },
          py: { xs: 0.75, sm: 1 },
          fontSize: { xs: '0.8125rem', sm: '0.875rem' },
          minWidth: 160,
          position: 'relative',
        }}
      />
    );
  };

  return (
    <Box
      sx={{
        width: '100%',
        px: { xs: 2, sm: 3, md: 4, lg: 5 },
        py: 3,
        backgroundColor: 'background.default',
        borderRadius: '32px',
      }}
    >
      {/* Connect Resource Dialog */}
      <ConnectResourceDialog open={connectDialogOpen} onClose={handleCloseConnectDialog} />

      {/* Databases Dialog */}
      <DatabasesDialog open={databasesDialogOpen} onClose={handleCloseDatabasesDialog} />

      {/* APIs Dialog */}
      <APIsDialog open={apisDialogOpen} onClose={handleCloseApisDialog} />

      {/* File Upload Dialog */}
      <FileUploadDialog open={uploadDialogOpen} onClose={handleCloseUploadDialog} />

      {/* Header Section */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: 3,
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Box sx={{ width: { xs: '100%', sm: 'auto' } }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: { xs: 'flex-start', sm: 'center' },
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 2 },
              mb: 1,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton
                sx={{
                  mr: -1,
                  color: 'text.primary',
                  backgroundColor: 'action.hover',
                  border: '1px solid',
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.selected',
                    borderColor: 'divider'
                  },
                }}
                onClick={() => window.history.back()}
              >
                <Iconify icon="eva:arrow-back-fill" width={24} />
              </IconButton>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Knowledge Base
              </Typography>
              <Chip
                label="DEVELOPER MODE"
                size="small"
                sx={{
                  background: (theme: any) =>
                    `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  color: 'common.white',
                  fontWeight: 'bold',
                  fontSize: '0.7rem',
                  height: 24,
                  borderRadius: '40px',
                }}
              />
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mx: 7 }}>
            Here you can upload your own files to help the agents to complete tasks
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <ConnectToCloudButton />
          <UploadButton />
        </Box>
      </Box>

      {/* Storage Progress */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 7, mb: 2 }}>
        <Box>
          <Typography sx={{ textWrap: 'nowrap', mx: 1 }} variant="subtitle2">
            Cloud storage
          </Typography>
        </Box>
        <Box sx={{ position: 'relative', width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={value}
            sx={{
              height: 24,
              borderRadius: 4,
              bgcolor: 'action.hover',
              '& .MuiLinearProgress-bar': {
                bgcolor: 'primary.main',
                borderRadius: 4,
              },
            }}
          />
          <Typography
            variant="subtitle2"
            sx={{
              position: 'absolute',
              top: -2,
              left: 0,
              right: 0,
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: value < 50 ? 'grey.700' : 'common.white',
            }}
          >
            17% (3.59 GB used)
          </Typography>
        </Box>
        <Box sx={{ mx: 1, display: 'flex', alignItems: 'center', textWrap: 'nowrap' }}>
          <Typography variant="subtitle2" sx={{ color: 'primary.main' }}>
            5 GB
          </Typography>
          <AppButton
            variant="text"
            size="small"
            sx={{ fontWeight: 'bold' }}
            label="Upgrade"
          />
        </Box>
      </Box>
      {/* Cloud Platforms */}
      <Box sx={{ mb: 4 }}>
        <CloudPlatformsCarousel platforms={CLOUD_PLATFORMS} />
      </Box>

      {/* Connection Options */}
      <Grid container spacing={2} sx={{ my: 2 }}>
        {CONNECTION_OPTIONS.map((option) => (
          <Grid item xs={12} sm={4} key={option.id}>
            <Card
              sx={{
                padding: '16px',
                height: '176px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                border: (theme: any) => `2px dashed ${theme.palette.divider}`,
                boxShadow: 'none',
                cursor: 'pointer',
                borderRadius: '8px',
                color: 'inherit',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
              onClick={() => {
                if (option.id === 'connect') {
                  handleOpenConnectDialog();
                } else if (option.id === 'databases') {
                  handleOpenDatabasesDialog();
                } else if (option.id === 'api') {
                  handleOpenApisDialog();
                }
              }}
            >
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 2,
                  color: option.color,
                }}
              >
                <Iconify icon={option.icon} width={60} height={60} />
              </Box>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                {option.title}
              </Typography>
              <Typography variant="body2" align="center" sx={{ fontWeight: 'light' }}>
                {option.description}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Files Table Section */}
      <Box sx={{ mb: 3 }}>
        {/* Table Filters */}
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 2 }}>
          {/* 1. File Type */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('File Type')}
            </Typography>
            <TextField
              select
              fullWidth
              size="small"
              value={fileType}
              onChange={(e) => setFileType(e.target.value)}
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            >
              <MenuItem value="pdf">PDF</MenuItem>
              <MenuItem value="docx">DOCX</MenuItem>
              <MenuItem value="xlsx">XLSX</MenuItem>
            </TextField>
          </Box>

          {/* 2. Date Picker */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Date')}
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                value={new Date(selectedDate)}
                onChange={(newDate) => {
                  if (newDate) {
                    const formattedDate = new Intl.DateTimeFormat('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric',
                    }).format(newDate);
                    setSelectedDate(formattedDate);
                  }
                }}
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true,
                    InputProps: { sx: { borderRadius: 1 } },
                    sx: {
                      '& .MuiSvgIcon-root': {
                        color: 'primary.main',
                      },
                    },
                  },
                  openPickerButton: { size: 'small' },
                }}
              />
            </LocalizationProvider>
          </Box>

          {/* 3. Search Field */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Search')}
            </Typography>
            <TextField
              placeholder="Search..."
              size="small"
              fullWidth
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify
                      icon="eva:search-fill"
                      width={20}
                      sx={{
                        color: 'text.secondary',
                      }}
                    />
                  </InputAdornment>
                ),
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>

        {/* Results Count and Category Filter */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {' 30 '}
          <Typography variant="body2" sx={{ mx: 1, color: 'text.secondary' }}>
            results found
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2">Category:</Typography>
            {selectedCategory && (
              <Chip
                label={selectedCategory}
                onDelete={handleClearCategory}
                size="small"
                sx={{
                  bgcolor: 'action.hover',
                  color: 'text.primary',
                  '& .MuiChip-deleteIcon': {
                    color: 'text.primary',
                    '&:hover': {
                      color: 'text.secondary',
                    },
                  },
                }}
              />
            )}

            <Box
              sx={{
                display: 'flex',
                gap: '5px',
                alignItems: 'center',
                ml: { xs: 0, sm: '10px' },
                cursor: 'pointer',
              }}
              onClick={handleClearCategory}
            >
              <Iconify
                width="20px"
                height="20px"
                icon="material-symbols:delete-outline"
                sx={{
                  color: 'error.main',
                }}
              />
              <Typography
                color="error"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                }}
              >
                Clear
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Files Table */}
        <KnowledgeBaseTable />
      </Box>
    </Box>
  );
}
