import type { BoxProps } from '@mui/material/Box';
import type { NavSectionProps } from 'src/components/nav-section';
import type { AutocompleteRenderOptionState } from '@mui/material/Autocomplete';
import type { SyntheticEvent } from 'react';

import { useState, useCallback, useMemo } from 'react';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';

import Box from '@mui/material/Box';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useTheme, styled } from '@mui/material/styles';
import InputAdornment from '@mui/material/InputAdornment';
import ListItemText from '@mui/material/ListItemText';
import ListItem from '@mui/material/ListItem';

import { useRouter } from 'src/routes/hooks';
import { isExternalLink } from 'src/routes/utils';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';
import { Scrollbar } from 'src/components/scrollbar';
import { SearchNotFound } from 'src/components/search-not-found';

import { applyFilter, getAllItems, groupItems } from './utils';

// ----------------------------------------------------------------------

// Styled components
const StyledAutocomplete = styled(Autocomplete<ItemProps, false, false, false>)(({ theme }) => ({
  width: '100%',
  margin:'10px',
  '& .MuiInputBase-root': {
    height: 48,
    borderRadius: 20,
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
    border: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.paper,
    // transition: theme.transitions.create(['box-shadow']),
   
   
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
}));

// ----------------------------------------------------------------------

export type SearchbarProps = BoxProps & {
  data?: NavSectionProps['data'];
};

interface ItemProps {
  group: string;
  title: string;
  path: string;
}

export function Searchbar({ data: navItems = [], sx, ...other }: SearchbarProps) {
  const theme = useTheme();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOption, setSelectedOption] = useState<ItemProps | null>(null);

  // Get all items from navigation data
  const allItems = useMemo(() => getAllItems({ data: navItems }), [navItems]);

  // Filter items based on search query
  const dataFiltered = useMemo(
    () => applyFilter({ inputData: allItems, query: searchQuery }),
    [allItems, searchQuery]
  );

  // Group filtered items by category
  const groupedItems = useMemo(() => groupItems(dataFiltered), [dataFiltered]);

  // Flatten grouped items for Autocomplete
  const options = useMemo(() => {
    return Object.keys(groupedItems).reduce((acc: ItemProps[], group) => {
      return [...acc, ...groupedItems[group]];
    }, []);
  }, [groupedItems]);

  const handleOptionClick = useCallback(
    (option: ItemProps | null) => {
      if (!option) return;

      if (isExternalLink(option.path)) {
        window.open(option.path);
      } else {
        router.push(option.path);
      }
      setSearchQuery('');
      setSelectedOption(null);
    },
    [router]
  );

  const handleInputChange = useCallback((_: SyntheticEvent, value: string) => {
    setSearchQuery(value);
  }, []);

  const handleChange = useCallback(
    (_: SyntheticEvent, value: ItemProps | null) => {
      setSelectedOption(value);
      handleOptionClick(value);
    },
    [handleOptionClick]
  );

  return (
    <Box sx={{ width: '70%', ...sx }} {...other}>
      <StyledAutocomplete
        fullWidth
        autoHighlight
        disableCloseOnSelect={false}
        popupIcon={null}
        options={options}
        getOptionLabel={(option: ItemProps) => option.title}
        groupBy={(option: ItemProps) => option.group}
        noOptionsText={<SearchNotFound query={searchQuery} />}
        isOptionEqualToValue={(option: ItemProps, value: ItemProps) => option.path === value.path}
        inputValue={searchQuery}
        value={selectedOption}
        onChange={handleChange}
        onInputChange={handleInputChange}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Search"
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon="eva:search-fill"
                    width={20}
                    sx={{ color: 'text.disabled', ml: 0.5 }}
                  />
                </InputAdornment>
              ),
            }}
          />
        )}
        renderOption={(props, option: ItemProps, { inputValue }: AutocompleteRenderOptionState) => {
          const { title, path, group } = option;
          const partsTitle = parse(title, match(title, inputValue));
          const partsPath = parse(path, match(path, inputValue));

          return (
            <ListItem
              {...props}
              key={path}
              sx={{
                borderWidth: 1,
                borderStyle: 'dashed',
                borderColor: 'transparent',
                borderBottomColor: theme.palette.divider,
                '&:hover': {
                  borderRadius: 1,
                  borderColor: theme.palette.primary.main,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <ListItemText
                primaryTypographyProps={{ typography: 'subtitle2', sx: { textTransform: 'capitalize' } }}
                secondaryTypographyProps={{ typography: 'caption', noWrap: true }}
                primary={
                  <Box component="span">
                    {partsTitle.map((part, index) => (
                      <Typography
                        key={index}
                        component="span"
                        color={part.highlight ? 'primary.main' : 'text.primary'}
                      >
                        {part.text}
                      </Typography>
                    ))}
                  </Box>
                }
                secondary={
                  <Box component="span">
                    {partsPath.map((part, index) => (
                      <Typography
                        key={index}
                        component="span"
                        color={part.highlight ? 'primary.main' : 'text.secondary'}
                      >
                        {part.text}
                      </Typography>
                    ))}
                  </Box>
                }
              />

              {group && <Label color="info">{group}</Label>}
            </ListItem>
          );
        }}
        ListboxComponent={Scrollbar as any}
        ListboxProps={{
          sx: { maxHeight: 400 },
        }}
      />
    </Box>
  );
}
