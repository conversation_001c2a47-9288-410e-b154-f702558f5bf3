import { TableProps } from '../../types';
import { AppTablePropsType } from './app-table';

// Define a generic type for the props
export interface AppTableRowProps<TRow> {
  columns: AppTablePropsType<TRow>['columns']; // Ensure columns contain keys of TRow
  data: TRow;
  selectRow?: {
    id: string;
    handleSelectRow: (row: TRow, table: TableProps) => void;
    table: TableProps;
  };
  index: number;
}
