import React, { useState } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Typography,
  useTheme,
} from '@mui/material';
import { Icon } from '@iconify/react';

interface FilterDropdownProps {
  label: string;
  options: string[];
  selectedIndex: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
}

export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  selectedIndex,
  onChange,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (index: number) => {
    onChange({} as React.SyntheticEvent, index);
    handleClose();
  };

  const selectedOption = options[selectedIndex] || options[0];

  return (
    <Box>
      <Button
        variant="outlined"
        onClick={handleClick}
        endIcon={<Icon icon="eva:chevron-down-fill" width={16} height={16} />}
        sx={{
          minWidth: 120,
          height: 36,
          px: 2,
          py: 1,
          borderRadius: '8px',
          border: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.paper',
          color: 'text.primary',
          textTransform: 'none',
          fontWeight: 500,
          fontSize: '0.875rem',
          '&:hover': {
            bgcolor: 'action.hover',
            borderColor: 'primary.main',
          },
          '&.Mui-focusVisible': {
            borderColor: 'primary.main',
            boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
          },
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Typography variant="body2" color="text.secondary" fontWeight={500}>
            {label}:
          </Typography>
          <Typography variant="body2" color="text.primary" fontWeight={600}>
            {selectedOption}
          </Typography>
        </Box>
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 160,
            maxHeight: 300,
            borderRadius: '12px',
            border: '1px solid',
            borderColor: 'divider',
            bgcolor: 'background.paper',
            boxShadow: theme.shadows[8],
            '& .MuiMenuItem-root': {
              px: 2,
              py: 1,
              borderRadius: '8px',
              mx: 1,
              my: 0.5,
              fontSize: '0.875rem',
              fontWeight: 500,
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
              '&.Mui-selected': {
                bgcolor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
              },
            },
          },
        }}
      >
        {options.map((option, index) => (
          <MenuItem
            key={index}
            selected={index === selectedIndex}
            onClick={() => handleMenuItemClick(index)}
          >
            {option}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};
