import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { CONFIG } from 'src/config-global';
import { Logo } from 'src/components/logo';

// ----------------------------------------------------------------------

export function AuthHeader() {
  return (
    <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 5, position: 'absolute', top: 30, left: 40 }}>
      <Logo />
      <Typography variant="h6" sx={{ color: 'text.primary' , pt:'27px'}}>
        {CONFIG.site.name}
      </Typography>
    </Stack>
  );
}
