import type { Theme, SxProps } from '@mui/material/styles';
import Box from '@mui/material/Box';

// ----------------------------------------------------------------------

export type NoLayoutProps = {
  sx?: SxProps<Theme>;
  children: React.ReactNode;
};

export function NoLayout({ sx, children }: NoLayoutProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        ...sx,
      }}
    >
      {children}
    </Box>
  );
}
