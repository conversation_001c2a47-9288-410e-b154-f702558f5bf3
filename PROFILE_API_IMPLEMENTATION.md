# Profile API Implementation

This document outlines the implementation of Profile APIs following the same structure used for Teams and Templates APIs.

## Files Created/Updated

### 1. API Layer (`src/services/api/use-profile-api.ts`)
- **Purpose**: Contains all profile-related API calls and types
- **Endpoints Implemented**:
  - `GET /users/me` → `useGetUserProfile()`
  - `PATCH /users/me` → `useUpdateUserProfile()`
  - `DELETE /users/me` → `useDeleteUserAccount()`
  - `POST /users/change-password` → `useChangePassword()`

**Key Features**:
- TypeScript interfaces for all request/response types
- Consistent error handling using `useApiServices`
- Automatic success/error notifications via snackbar
- Query invalidation for data consistency

### 2. View Hook (`src/sections/settings/view/use-profile-view.tsx`)
- **Purpose**: Manages state and business logic for profile-related UI components
- **Features**:
  - Form state management for profile updates and password changes
  - Avatar file handling with preview
  - Form validation (password matching, minimum length)
  - Edit mode toggle functionality
  - Change detection for unsaved modifications

### 3. Updated UI Components

#### Profile Settings (`src/sections/settings/view/profile-settings.tsx`)
- Connected to profile API for real-time data
- Edit mode with form validation
- Avatar upload functionality
- Loading and error states
- Save changes only when modifications detected

#### Password Settings (`src/sections/settings/view/password-settings.tsx`)
- Password change form with validation
- Password visibility toggles
- Real-time password matching validation
- Minimum length requirements
- Success/error feedback

#### Delete Account Settings (`src/sections/settings/view/delete-account-settings.tsx`)
- Confirmation dialog with typed confirmation
- Warning messages about data loss
- Proper error handling
- Disabled state during deletion process

### 4. Example Usage (`src/sections/profile/example-usage.tsx`)
- Demonstrates how to use all profile API hooks
- Shows proper error handling patterns
- Example of mutation callbacks
- Reference for API endpoints

## API Structure

### User Profile Interface (Actual Backend Response)
```typescript
interface UserProfile {
  id: number;
  misrajId: string | null;
  email: string;
  name: string;
  username: string;
  pictureUrl: string | null;
  role: string;
  developerMode: boolean;
  createdAt: string;
  updatedAt: string;
  pictureFileId: string | null;
  verifiedAt: string | null;
  pictureFile: any | null;
}
```

### Update Profile Payload (Backend Accepted Fields)
```typescript
interface UpdateProfilePayload {
  name?: string;
  username?: string;
  pictureUrl?: string;
}
```

### Profile Form Data (UI Form Management)
```typescript
interface ProfileFormData {
  name: string;
  username: string;
  pictureUrl?: string;
  // Additional fields for UI display (split from name)
  firstName: string;
  lastName: string;
}
```

### Change Password Payload (API Request)
```typescript
interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}
```

### Change Password Form Data (UI Form)
```typescript
interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string; // Used only for client-side validation
}
```

## Usage Examples

### Basic Profile Fetching
```typescript
const { useGetUserProfile } = useProfileApi();
const { data: profile, isLoading, error } = useGetUserProfile();
```

### Updating Profile
```typescript
const { useUpdateUserProfile } = useProfileApi();
const { mutate: updateProfile } = useUpdateUserProfile((data) => {
  console.log('Profile updated:', data);
});

// Only name and username are accepted by the backend
updateProfile({
  name: 'John Doe',
  username: 'johndoe'
});
```

### Changing Password
```typescript
const { useChangePassword } = useProfileApi();
const { mutate: changePassword } = useChangePassword();

// Note: confirmPassword is handled client-side only
changePassword({
  currentPassword: 'oldpass',
  newPassword: 'newpass'
});
```

### Deleting Account
```typescript
const { useDeleteUserAccount } = useProfileApi();
const { mutate: deleteAccount } = useDeleteUserAccount(() => {
  // Redirect to login page
});

deleteAccount({});
```

## Integration with Existing Settings

The profile API is fully integrated with the existing settings system:
- Settings navigation remains unchanged
- Components are drop-in replacements for existing static forms
- Maintains consistent UI/UX patterns
- Follows the same error handling and loading state patterns as Teams/Templates

## Error Handling

All API calls include:
- Automatic error snackbar notifications
- Component-level error states
- Loading indicators
- Proper TypeScript error types

## API Structure & Considerations

- **Backend Fields**: The backend accepts `name`, `username`, and `pictureUrl` fields for profile updates
- **UI Enhancement**: The UI splits the `name` field into `firstName` and `lastName` for better user experience, then combines them back into `name` for API requests
- **Profile Picture**: Uses `pictureUrl` field (not `avatar`) to match backend structure
- **Developer Mode**: Available in profile data but managed separately (not through profile update)
- **Email field**: Read-only (not user-editable)
- **Password changes**: Require current password verification
- **Account deletion**: Requires explicit confirmation
- **Loading states**: All mutations include proper loading states to prevent double-submission
- **Password confirmation**: `confirmPassword` is validated client-side only and not sent to the API

## Testing

To test the implementation:
1. Import and use the `ProfileApiExample` component
2. Verify all API endpoints respond correctly
3. Test form validation and error states
4. Confirm proper loading indicators
5. Validate success/error notifications

## Next Steps

1. **Backend Integration**: Ensure backend APIs match the expected request/response formats
2. **Avatar Upload**: Implement file upload functionality for profile pictures
3. **Form Validation**: Add more comprehensive client-side validation
4. **Testing**: Add unit tests for API hooks and components
5. **Documentation**: Update API documentation with new endpoints
