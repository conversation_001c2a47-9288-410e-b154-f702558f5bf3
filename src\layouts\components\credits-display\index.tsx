import { Box, Typography, Button, useMediaQuery, useTheme } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useTranslation } from 'react-i18next';

// ----------------------------------------------------------------------

export interface CreditsDisplayProps {
  credits: number;
  onClick?: () => void;
}

export function CreditsDisplay({ credits, onClick }: CreditsDisplayProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const isExtraSmall = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Button
      onClick={onClick}
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: { xs: 0.5, sm: 0.75, md: 1 },
        py: { xs: 0.5, sm: 0.75, md: 1 },
        px: { xs: 1, sm: 1.5, md: 2 },
        borderRadius: 30,
        background: 'linear-gradient(90deg, #FF6B35 0%, #FF9F7E 100%)',
        color: 'white',
        '&:hover': {
          background: 'linear-gradient(90deg, #E55A2A 0%, #FF8F6E 100%)',
        },
        minWidth: 'auto',
        height: { xs: 32, sm: 36, md: 40 },
        transition: theme.transitions.create(['padding', 'height', 'gap'], {
          duration: theme.transitions.duration.shorter,
        }),
      }}
    >
      <Iconify
        icon="eva:cube-fill"
        sx={{
          flexShrink: 0,
          width: { xs: 16, sm: 18, md: 20 },
          height: { xs: 16, sm: 18, md: 20 },
          transition: theme.transitions.create('width', {
            duration: theme.transitions.duration.shorter,
          }),
        }}
      />
      <Typography
        variant="subtitle2"
        fontWeight="bold"
        sx={{
          fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8125rem' },
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          transition: theme.transitions.create('fontSize', {
            duration: theme.transitions.duration.shorter,
          }),
        }}
      >
        {credits.toLocaleString()} {isExtraSmall ? '' : t('common.credits')}
      </Typography>
    </Button>
  );
}
