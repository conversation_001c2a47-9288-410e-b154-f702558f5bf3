import { useState } from 'react';
import type { SxProps, Theme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Badge from '@mui/material/Badge';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';

import { m } from 'framer-motion';
import { varHover } from 'src/components/animate';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

// Extended NotificationItemProps with description field
type NotificationItemProps = {
  id: string;
  type: string;
  title: string;
  description?: string;
  category: string; // This is actually the notification time
  isUnRead: boolean;
  avatarUrl: string | null;
  createdAt: string | number | null;
};

// ----------------------------------------------------------------------

// Mock data for testing
const MOCK_NOTIFICATIONS: NotificationItemProps[] = [
  {
    id: '1',
    title: '<p><strong>Question answered!</strong></p>',
    description: 'Your question #123456 has been answered...',
    category: '9:32 AM',
    type: 'question',
    isUnRead: true,
    avatarUrl: null,
    createdAt: new Date().getTime() - 1000 * 60 * 5, // 5 minutes ago
  },
  {
    id: '2',
    title: '<p><strong>Question answered!</strong></p>',
    description: 'Your question #123456 has been answered...',
    category: '9:32 AM',
    type: 'question',
    isUnRead: false,
    avatarUrl: null,
    createdAt: new Date().getTime() - 1000 * 60 * 30, // 30 minutes ago
  },
  {
    id: '3',
    title: '<p><strong>Question answered!</strong></p>',
    description: 'Your question #123456 has been answered...',
    category: '9:32 AM',
    type: 'question',
    isUnRead: false,
    avatarUrl: null,
    createdAt: new Date().getTime() - 1000 * 60 * 60 * 3, // 3 hours ago
  },
  {
    id: '4',
    title: '<p><strong>Question answered!</strong></p>',
    description: 'Your question #123456 has been answered...',
    category: '9:32 AM',
    type: 'question',
    isUnRead: false,
    avatarUrl: null,
    createdAt: new Date().getTime() - 1000 * 60 * 60 * 24, // 1 day ago
  },
  {
    id: '5',
    title: '<p><strong>Question answered!</strong></p>',
    description: 'Your question #123456 has been answered...',
    category: '1d ago',
    type: 'question',
    isUnRead: false,
    avatarUrl: null,
    createdAt: new Date().getTime() - 1000 * 60 * 60 * 48, // 2 days ago
  },
];

// ----------------------------------------------------------------------

type NotificationsMenuProps = {
  data?: NotificationItemProps[];
  sx?: SxProps<Theme>;
};

export function NotificationsMenu({ data = MOCK_NOTIFICATIONS, sx }: NotificationsMenuProps) {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const [notifications, setNotifications] = useState(data);
  const totalUnRead = notifications.filter((item) => item.isUnRead === true).length;

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMarkAllAsRead = () => {
    setNotifications(
      MOCK_NOTIFICATIONS.map((notification) => ({ ...notification, isUnRead: false }))
    );
  };

  const todayNotifications = MOCK_NOTIFICATIONS.filter(
    (n) => new Date(n.createdAt as number).toDateString() === new Date().toDateString()
  );

  const yesterdayNotifications = MOCK_NOTIFICATIONS.filter((n) => {
    const notifDate = new Date(n.createdAt as number);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return notifDate.toDateString() === yesterday.toDateString();
  });

  const olderNotifications = notifications.filter((n) => {
    const notifDate = new Date(n.createdAt as number);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return (
      notifDate.toDateString() !== yesterday.toDateString() &&
      notifDate.toDateString() !== new Date().toDateString()
    );
  });

  // Handle notification click
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const handleNotificationClick = (id: string) => {
    // Toggle selection
    setSelectedId(id === selectedId ? null : id);

    // Mark as read when clicked
    if (notifications.find((n) => n.id === id)?.isUnRead) {
      setNotifications(
        notifications.map((notification) =>
          notification.id === id ? { ...notification, isUnRead: false } : notification
        )
      );
    }
  };

  // Render a notification item
  const renderNotificationItem = (notification: NotificationItemProps) => (
    <Box
      key={notification.id}
      onClick={() => handleNotificationClick(notification.id)}
      sx={{
        position: 'relative',
        cursor: 'pointer',
        bgcolor: selectedId === notification.id ? 'rgba(255, 226, 216, 1)' : 'background.paper', // Change background when selected
        borderBottom: '1px solid',
        borderColor: 'divider',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          p: 2,
        }}
      >
        <Avatar
          sx={{
            width: 40,
            height: 40,
            mr: 2,
            bgcolor: 'background.paper',
            border:
              selectedId === notification.id
                ? ''
                : notification.isUnRead
                  ? '1px solid'
                  : '1px solid',
            // Change color to primary when selected, otherwise use default colors
            color:
              selectedId === notification.id
                ? 'primary.main'
                : notification.isUnRead
                  ? 'text.secondary'
                  : 'text.secondary',
          }}
        >
          <Iconify
            icon={selectedId === notification.id ? 'solar:bell-bold' : 'solar:bell-outline'}
            width={24}
          />
        </Avatar>

        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <Box
            dangerouslySetInnerHTML={{ __html: notification.title }}
            sx={{
              typography: 'subtitle2',
              mb: 0.5,
              '& p': { m: 0 },
            }}
          />

          <Typography variant="body2">{notification.description}</Typography>

          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
              display: 'flex',
              alignItems: 'center',
              mt: 0.5,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: 'text.disabled',
              }}
            >
              {notification.category}
            </Typography>

            {selectedId === notification.id && (
              <Typography
                variant="caption"
                onClick={(e) => {
                  e.stopPropagation();
                  // Mark as read when clicked
                  setNotifications(
                    notifications.map((notif) =>
                      notif.id === notification.id ? { ...notif, isUnRead: false } : notif
                    )
                  );
                }}
                sx={{
                  ml: 1,
                  color: 'primary.main',
                  fontWeight: 'bold',

                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                {t('components.notifications.markAsRead')}
              </Typography>
            )}
          </Stack>
        </Box>
      </Box>
    </Box>
  );

  // Render a group of notifications with a label
  const renderNotificationGroup = (title: string, items: NotificationItemProps[]) => {
    if (items.length === 0) return null;

    return (
      <Box key={title}>
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            px: 2.5,
            py: 1,
            color: 'text.secondary',
            fontWeight: 'fontWeightMedium',
          }}
        >
          {title}
        </Typography>

        {items.map(renderNotificationItem)}
      </Box>
    );
  };

  const renderList = (
    <Scrollbar sx={{ height: { xs: 320, sm: 400 } }}>
      <Box sx={{ p: 0, m: 0 }}>
        {renderNotificationGroup(t('components.notifications.today'), todayNotifications)}
        {renderNotificationGroup(t('components.notifications.yesterday'), yesterdayNotifications)}
        {/* {olderNotifications.length > 0 && renderNotificationGroup('Older', olderNotifications)} */}

        {notifications.length === 0 && (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="subtitle1">
              {t('components.notifications.noNotifications')}
            </Typography>
          </Box>
        )}
      </Box>
    </Scrollbar>
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={handleClick}
        sx={{
          ...(open && {
            '&:before': {
              zIndex: 1,
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '50%',
              position: 'absolute',
              border: '1px solid',
            },
          }),
          ...sx,
        }}
      >
        <Badge badgeContent={totalUnRead} color="error">
          <Iconify icon="solar:bell-outline" width={24} />
        </Badge>
      </IconButton>

      <Menu
        id="notifications-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              width: 400,
              overflow: 'visible',
              mt: 1.5,
              borderRadius: 2,
              boxShadow: (theme) => theme.customShadows.dropdown,
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          },
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            borderRadius: 2,
            py: 2,
            px: 2.5,
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          }}
        >
          <Typography variant="h5">{t('components.notifications.title')}</Typography>

          <Stack direction="row">
            {!!totalUnRead && (
              <Button
                color="inherit"
                size="small"
                onClick={handleMarkAllAsRead}
                sx={{
                  typography: 'body3',
                  fontWeight: 'normal',
                  textTransform: 'none',
                }}
              >
                {t('components.notifications.markAllAsRead')}
              </Button>
            )}

            <Button
              color="inherit"
              size="small"
              onClick={handleClose}
              sx={{
                typography: 'body3',
                fontWeight: 'normal',
                textTransform: 'none',
              }}
            >
              • {t('components.notifications.viewAll')}
            </Button>
          </Stack>
        </Stack>

        {/* {renderTabs} */}
        {renderList}
      </Menu>
    </>
  );
}
