import { useQueryClient } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { useDeveloperMode } from 'src/contexts/developer-mode-context';
import {
  useProfileApi,
  UserProfile,
  ProfileFormData,
  ChangePasswordFormData,
  GenerateUploadFile,
} from 'src/services/api/use-profile-api';
import { useGetPictureurl } from 'src/services/hooks/use-get-picture-url';

export const useProfileView = () => {
  // Form state for profile update

  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    name: '',
    username: '',
    firstName: '',
    lastName: '',
  });

  // Form state for password change
  const [passwordForm, setPasswordForm] = useState<ChangePasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // UI state
  const [isEditing, setIsEditing] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');

  const queryClient = useQueryClient();
  // Use the profile API hook
  const {
    useGetUserProfile,
    useUpdateUserProfile,
    useDeleteUserAccount,
    useChangePassword,
    useUploadFile,
    generateDownloadUrl,
    useToggleDeveloperMode,
  } = useProfileApi();

  const { mutate: uploadFile, isPending: isPendingFile } = useUploadFile({
    onSuccess: (fileUrl) => {
      setAvatarPreview(fileUrl);
    },
  });
  const { mutate: mutateToggleDeveloperMode, isPending: isPendingTogggleDeveloperMode } =
    useToggleDeveloperMode();

  // Get user profile data
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useGetUserProfile();

  // const [developerModeCheck, setDeveloperModeCheck] = useState(userProfile?.developerMode || false);
  const { setDeveloperMode, developerMode } = useDeveloperMode();
  useEffect(() => {
    if (userProfile?.pictureFile?.key) {
      generateDownloadUrl(userProfile.pictureFile.key).then(setAvatarPreview).catch(console.error);
    }
  }, [userProfile?.pictureFile?.key]);

  // Update profile mutation
  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    error: updateError,
  } = useUpdateUserProfile((data) => {
    setIsEditing(false);
    refetchProfile();
  });

  // Change password mutation
  const {
    mutate: changePassword,
    isPending: isChangingPassword,
    error: passwordError,
  } = useChangePassword(() => {
    // Reset password form on success
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
  });

  // Delete account mutation
  const {
    mutate: deleteAccount,
    isPending: isDeletingAccount,
    error: deleteError,
  } = useDeleteUserAccount();

  // Initialize form with user data when profile loads
  useEffect(() => {
    if (userProfile) {
      // Split name into first and last name for UI
      const nameParts = (userProfile.name || '').split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      setProfileForm({
        name: userProfile.name || '',
        username: userProfile.username || '',
        firstName,
        lastName,
      });

      // Set avatar preview if user has a picture
    }
  }, [userProfile]);

  // Handle profile form changes
  const handleProfileFormChange = (field: keyof ProfileFormData, value: string) => {
    setProfileForm((prev) => {
      const updated = {
        ...prev,
        [field]: value,
      };

      // Auto-update name when first/last name changes
      if (field === 'firstName' || field === 'lastName') {
        updated.name =
          `${field === 'firstName' ? value : prev.firstName} ${field === 'lastName' ? value : prev.lastName}`.trim();
      }

      return updated;
    });
  };

  // Handle password form changes
  const handlePasswordFormChange = (field: keyof ChangePasswordFormData, value: string) => {
    setPasswordForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = (file: File | null) => {
    setAvatarFile(file);

    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarPreview(userProfile?.pictureUrl || '');
    }
    uploadFile(file as File);
  };

  // Handle profile update submission
  const handleUpdateProfile = () => {
    // Only send fields that the backend accepts
    const payload = {
      name: profileForm.name,
      username: profileForm.username,
    };

    // If there's a new avatar file, we would typically upload it first
    // For now, we'll just include the picture URL in the payload
    if (avatarFile) {
      // In a real implementation, you'd upload the file and get a URL
      // payload.pictureUrl = uploadedPictureUrl;
    }

    updateProfile(payload);
  };

  // Handle password change submission
  const handleChangePassword = () => {
    // Validate passwords match
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      // Handle validation error
      return;
    }

    // Only send required fields to API (exclude confirmPassword)
    changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
    });
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    // In a real implementation, you might want to show a confirmation dialog
    deleteAccount(); // The delete endpoint might not need a payload
  };

  // Toggle edit mode
  const handleToggleEdit = () => {
    if (isEditing) {
      // If canceling edit, reset form to original values
      if (userProfile) {
        const nameParts = (userProfile.name || '').split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        setProfileForm({
          name: userProfile.name || '',
          username: userProfile.username || '',
          firstName,
          lastName,
        });
        setAvatarPreview(userProfile.pictureUrl || '');
        setAvatarFile(null);
      }
    }
    setIsEditing(!isEditing);
  };

  // Check if profile form has changes
  const hasProfileChanges = userProfile
    ? profileForm.name !== (userProfile.name || '') ||
      profileForm.username !== (userProfile.username || '') ||
      avatarFile !== null
    : false;

  // Check if password form is valid
  const isPasswordFormValid =
    passwordForm.currentPassword.length > 0 &&
    passwordForm.newPassword.length >= 8 &&
    passwordForm.newPassword === passwordForm.confirmPassword;

  return {
    // Data
    userProfile,
    profileForm,
    passwordForm,
    avatarPreview,

    // Loading states
    isLoadingProfile,
    isUpdatingProfile,
    isChangingPassword,
    isDeletingAccount,

    // Error states
    profileError,
    updateError,
    passwordError,
    deleteError,

    // UI state
    isEditing,
    hasProfileChanges,
    isPasswordFormValid,

    // Actions
    handleProfileFormChange,
    handlePasswordFormChange,
    handleAvatarChange,
    handleUpdateProfile,
    handleChangePassword,
    handleDeleteAccount,
    handleToggleEdit,
    refetchProfile,
    isPendingFile,
    isPendingTogggleDeveloperMode,
    mutateToggleDeveloperMode,
    queryClient,

    setDeveloperMode,
    developerMode,
  };
};

export type { UserProfile, ProfileFormData, ChangePasswordFormData };
