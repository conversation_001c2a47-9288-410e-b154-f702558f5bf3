import { useState, useCallback, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { signOut } from 'src/auth/context/jwt';
import { useMockedUser, useAuthContext } from 'src/auth/hooks';
import { Iconify } from 'src/components/iconify';
import { useAccountMenu } from 'src/contexts/account-menu-context';

// ----------------------------------------------------------------------

const MenuRoot = styled('div')(({ theme }) => ({

  border:'1px solid rgba(255, 111, 60, 0.48)',
  borderRadius:'50%' ,


}));

const MenuButton = styled(IconButton)(({ theme }) => ({
  padding: 0,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[3],
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[6],
    transform: 'scale(1.05)',
  },
}));

// ----------------------------------------------------------------------

export function GlobalAccountMenu() {
  const router = useRouter();
  const location = useLocation();
  const { user } = useMockedUser();
  const { checkUserSession } = useAuthContext();
  const { t } = useTranslation();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [showMenu, setShowMenu] = useState(true);
  const open = Boolean(anchorEl);

  const { isGlobalMenuVisible } = useAccountMenu();

  // Hide the menu on auth pages
  useEffect(() => {
    const isAuthPage = location.pathname.includes('/auth/');
    setShowMenu(!isAuthPage && isGlobalMenuVisible);
  }, [location, isGlobalMenuVisible]);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();
      setAnchorEl(null); // Close the menu
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error during logout:', error);
    }
  }, [checkUserSession, router]);

  const handleClickMenuItem = (path: string) => {
    handleClose();
    router.push(path);
  };

  if (!showMenu) {
    return null;
  }

  return (
    <MenuRoot>
      <MenuButton
        onClick={handleClick}
        sx={{

          ...(open && {
            '&:before': {
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '50%',

              // position: 'absolute',
              // bgcolor: (theme) => theme.palette.primary.lighter,
            },
          }),
        }}
      >
        <Avatar
          src={user?.photoURL}
          alt={user?.displayName}
          sx={{
            width: 40,
            height: 40,
            border: (theme) => `solid 2px ${theme.palette.background.default}`,
          }}
        >
          {user?.displayName?.charAt(0).toUpperCase()}
        </Avatar>
      </MenuButton>

      <Menu
        id="global-account-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              width: 220,
              overflow: 'visible',
              mt: 1.5,
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          },
        }}
      >
        <Box sx={{ px: 2, py: 1.5 }}>
          <Typography variant="subtitle1" noWrap>
            {user?.displayName}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
            {user?.email}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={() => handleClickMenuItem(paths.dashboard.profile.root)} sx={{ m: 1 }}>
          <Iconify icon="mdi:account-outline" width={24} sx={{ mr: 2 }} />
          {t('components.accountMenu.profile')}
        </MenuItem>

        <MenuItem onClick={() => handleClickMenuItem(paths.dashboard.root)} sx={{ m: 1 }}>
          <Iconify icon="mdi:view-dashboard-outline" width={24} sx={{ mr: 2 }} />
          {t('components.accountMenu.dashboard')}
        </MenuItem>

        <MenuItem onClick={() => handleClickMenuItem(paths.dashboard.profile.settings)} sx={{ m: 1 }}>
          <Iconify icon="mdi:cog-outline" width={24} sx={{ mr: 2 }} />
          {t('components.accountMenu.settings')}
        </MenuItem>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={handleLogout} sx={{ m: 1 }}>
          <Iconify icon="mdi:logout-variant" width={24} sx={{ mr: 2 }} />
          {t('components.accountMenu.logout')}
        </MenuItem>
      </Menu>
    </MenuRoot>
  );
}
