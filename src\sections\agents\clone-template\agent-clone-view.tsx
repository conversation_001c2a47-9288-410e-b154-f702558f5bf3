import { useState } from 'react';
import {
  Box,
  Card,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  Stack,
  Divider,
  Radio,
  CircularProgress,
  Chip,
  Select,
  MenuItem,
  FormControl,
  Menu,
  Button,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

import { AppButton } from 'src/components/common/app-button';
import { paths } from 'src/routes/paths';
import { TemplateTool } from 'src/services/api/use-templates-api';

import { ToolConfigDialog } from './components/tool-config-dialog';
import useAgentCloneView from './use-agent-clone-view';
import ConfigureToolsDialog from './components/configure-tools-dialog';

// Mock data for tools

const AgentCloneView = () => {
  const {
    templatesResponseById,
    theme,
    toolsExpanded,
    setToolsExpanded,
    searchQuery,
    setSearchQuery,
    filteredTools,
    // getStatusColor,
    // getStatusBgColor,
    setRequirementsExpanded,
    requirementsExpanded,
    setSpecialRequest,
    specialRequest,
    handleConfigureClick,
    selectedTool,
    configDialogOpen,
    handleCloseConfigDialog,
    isLoading,
    navigate,
    isPending,
    handleContinueToTheChat,
    agentName,
    isLoadingToolsAuth,
    expandedTool,
    handleToolExpand,
    dataAuthTools,
    handleToolConfigSelect,
    selectedToolConfigs,
    dataConfigTools,
    isLoadingConfigTools,
    stateTool,
    code,
    isToolLinked,
    areAllToolsLinked,

    toolConfigSearchName,
    handleSearchQueryName,
    setSelectedToolId,
    setSelectedTool,
    selectedToolForDropdown,
    setSelectedToolForDropdown,
    anchorEl,
    setAnchorEl,
    handleDropdownClose,
    handleDropdownClick,
  } = useAgentCloneView();

  return (
    <>
      {/* header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Iconify
            onClick={() => navigate(paths.dashboard.agents.templates)}
            sx={{ cursor: 'pointer' }}
            icon="fluent:ios-arrow-24-regular"
          />
          <Typography variant="h6" fontWeight={600} color="#333">
            {agentName}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: '10px' }}>
          <Iconify icon="qlementine-icons:menu-dots-16" />
          <Iconify icon="mingcute:arrow-right-line" />
        </Box>
      </Box>
      <Divider sx={{ my: '20px' }} />

      <Typography variant="h3">Build your agent</Typography>
      <Divider sx={{ my: '20px' }} />

      {/* main card */}
      <Box>
        <Box sx={{ maxWidth: '75%', mx: 'auto' }}>
          {/* Main Card */}
          <Card
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow:
                theme.palette.mode === 'dark'
                  ? '0 4px 20px rgba(0, 0, 0, 0.3)'
                  : '0 4px 20px rgba(0, 0, 0, 0.1)',
              bgcolor: theme.palette.background.neutral,
            }}
          >
            {/* Tools & Requirements Header */}
            <Box sx={{ mb: 3 }}>
              <Typography
                sx={{
                  fontWeight: 700,
                  fontSize: '19px',
                  lineHeight: '24px',
                  color: theme.palette.text.primary,
                  mb: 0.5,
                }}
              >
                Tools & Requirements
              </Typography>
              <Typography
                sx={{
                  fontSize: '13px',
                  fontWeight: 400,
                  color: theme.palette.text.secondary,
                }}
              >
                Set up your tool configurations
              </Typography>
            </Box>

            {/* Tools Accordion */}
            {isLoading ? (
              <LoadingScreen />
            ) : (
              <Card sx={{ padding: '24px' }}>
                <Accordion
                  expanded={toolsExpanded}
                  onChange={() => setToolsExpanded(!toolsExpanded)}
                  sx={{
                    mb: 2,
                    boxShadow: 'none',
                    // border: `1px solid ${theme.palette.divider}`,
                    borderRadius: '8px !important',

                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                    sx={{
                      backgroundColor:
                        theme.palette.mode === 'dark' ? theme.palette.grey[800] : 'white',
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                      },
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight={600}>
                      Tools
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3, bgcolor: '#FAF9FA', borderRadius: '16px' }}>
                    {/* Search Field */}
                    <TextField
                      fullWidth
                      placeholder="Search..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify
                              icon="eva:search-fill"
                              width={20}
                              sx={{ color: theme.palette.text.disabled }}
                            />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          backgroundColor:
                            theme.palette.mode === 'dark' ? theme.palette.grey[900] : '#F9FAFB',
                          '& fieldset': {
                            borderColor: theme.palette.divider,
                          },
                          '&:hover fieldset': {
                            borderColor: theme.palette.primary.main,
                          },
                        },
                      }}
                    />

                    {/* Tools List as Accordions */}
                    <Stack spacing={1} sx={{ backgroundColor: 'white' }}>
                      {filteredTools?.map((tool) => (
                        <Stack
                          sx={{
                            background: '#F1EFF3',
                            pl: 1.5,
                            pb: 1.5,
                            pr: 1.5,
                            borderRadius: '22px',
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              width: '100%',
                              my: '20px',
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Iconify icon={tool?.tool?.icon} width={24} height={24} />
                              <Typography variant="body2" fontWeight={500}>
                                {tool?.tool?.name}
                              </Typography>
                            </Box>
                            {isToolLinked(tool.id) && (
                              <Chip label="linked" color="success" variant="soft" />
                            )}
                            {!isToolLinked(tool.id) && (
                              <Chip label="Configuration Required" color="warning" variant="soft" />
                            )}
                          </Box>
                          {isLoadingToolsAuth && tool?.id === selectedTool?.id && (
                            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                              <CircularProgress />
                            </Box>
                          )}

                          <Box
                            sx={{
                              border: `1px solid #CAC6D0`,
                              borderRadius: '8px',
                              backgroundColor: theme.palette.background.paper,
                            }}
                          >
                            <AppButton
                              fullWidth
                              onClick={(e) => handleDropdownClick(e, tool)}
                              endIcon={<Iconify icon="eva:chevron-down-fill" />}
                              sx={{
                                justifyContent: 'space-between',
                                p: 1,
                                textTransform: 'none',
                                color: theme.palette.text.primary,
                                backgroundColor: 'white',
                                '&:hover': {
                                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                },
                              }}
                              label={(() => {
                                const selectedConfig = selectedToolConfigs.find(
                                  (config) => config.toolId === tool.toolId
                                );

                                if (selectedConfig) {
                                  // First try to use the stored configName
                                  if (selectedConfig.configName) {
                                    return selectedConfig.configName;
                                  }
                                  // Fallback to searching in dataConfigTools
                                  if (dataConfigTools) {
                                    const configTool = dataConfigTools.find(
                                      (configTool: any) =>
                                        configTool.id === selectedConfig.toolConfigId
                                    );
                                    if (configTool?.name) {
                                      return configTool.name;
                                    }
                                  }
                                  // Final fallback
                                  return `Config ${selectedConfig.toolConfigId}`;
                                }
                                return 'Configure';
                              })()}
                            />
                          </Box>

                          <Menu
                            anchorEl={anchorEl}
                            open={Boolean(anchorEl) && selectedToolForDropdown?.id === tool.id}
                            onClose={handleDropdownClose}
                            PaperProps={{
                              sx: {
                                width: anchorEl?.getBoundingClientRect().width || 300,
                                maxHeight: 400,
                                mt: 1,
                                borderRadius: 2,
                              },
                            }}
                          >
                            <Box sx={{ p: 2, backgroundColor: 'white' }}>
                              {isLoadingConfigTools ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                  <CircularProgress size={24} />
                                </Box>
                              ) : (
                                <>
                                  <Box sx={{ mb: 2 }}>
                                    <TextField
                                      fullWidth
                                      placeholder="Search"
                                      value={toolConfigSearchName}
                                      onChange={(e) => handleSearchQueryName(e.target.value)}
                                      InputProps={{
                                        startAdornment: (
                                          <InputAdornment position="start">
                                            <Iconify
                                              icon="eva:search-fill"
                                              width={20}
                                              sx={{ color: 'inherit' }}
                                            />
                                          </InputAdornment>
                                        ),
                                      }}
                                      sx={{
                                        '& .MuiOutlinedInput-root': {
                                          borderColor: 'transparent',
                                          borderRadius: 1,
                                          bgcolor: 'rgba(24, 0, 72, 0.08)',
                                          height: 40,
                                          fontSize: '0.9rem',
                                        },
                                      }}
                                    />
                                  </Box>

                                  {dataConfigTools && dataConfigTools.length > 0 ? (
                                    <>
                                      <Typography
                                        sx={{
                                          fontWeight: 600,
                                          mb: 2,
                                          fontSize: '0.9rem',
                                          fontWieght: 600,
                                        }}
                                      >
                                        Your Configurations
                                      </Typography>
                                      {dataConfigTools?.map((authTool: any) => (
                                        <MenuItem
                                          key={authTool.id}
                                          onClick={() => {
                                            handleToolConfigSelect(
                                              authTool.id,
                                              tool.toolId,
                                              authTool.name
                                            );
                                            handleDropdownClose();
                                          }}
                                          sx={{
                                            borderRadius: 1,
                                            mb: 0.5,
                                            backgroundColor: selectedToolConfigs.some(
                                              (config) => config.toolConfigId === authTool.id
                                            )
                                              ? 'primary.main'
                                              : '',
                                            color: selectedToolConfigs.some(
                                              (config) => config.toolConfigId === authTool.id
                                            )
                                              ? 'white'
                                              : 'black',
                                            '&:hover': {
                                              backgroundColor: 'primary.main',
                                              color: 'white',
                                            },
                                          }}
                                        >
                                          <Box
                                            sx={{
                                              display: 'flex',
                                              width: '100%',
                                              justifyContent: 'space-between',
                                              alignItems: 'center',
                                            }}
                                          >
                                            <Typography>
                                              {authTool.name || `Config ${authTool.id}`}
                                            </Typography>
                                            <Box>
                                              {selectedToolConfigs.some(
                                                (config) => config.toolConfigId === authTool.id
                                              ) ? (
                                                <Iconify
                                                  icon="dashicons:yes"
                                                  style={{
                                                    color: 'white',
                                                    width: '32px',
                                                    height: '32px',
                                                  }}
                                                />
                                              ) : (
                                                <></>
                                              )}
                                            </Box>
                                          </Box>
                                        </MenuItem>
                                      ))}
                                    </>
                                  ) : (
                                    <Box sx={{ textAlign: 'center', p: 2 }}>
                                      <Typography variant="body2" color="text.secondary">
                                        No configurations available
                                      </Typography>
                                    </Box>
                                  )}

                                  <Divider sx={{ my: 1 }} />
                                  <MenuItem
                                    onClick={() => {
                                      handleConfigureClick(tool);
                                      handleDropdownClose();
                                    }}
                                    sx={{
                                      borderRadius: 1,
                                      color: 'black',
                                      '&:hover': {
                                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                      },
                                    }}
                                  >
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      {isLoadingToolsAuth ? (
                                        <CircularProgress size={16} />
                                      ) : (
                                        <Iconify icon="ic:baseline-plus" width={16} />
                                      )}
                                      <Typography variant="body2">Add New Configure</Typography>
                                    </Box>
                                  </MenuItem>
                                </>
                              )}
                            </Box>
                          </Menu>
                        </Stack>
                      ))}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Requirements Accordion */}
                <Accordion
                  expanded={requirementsExpanded}
                  onChange={() => setRequirementsExpanded(!requirementsExpanded)}
                  sx={{
                    mb: 3,
                    boxShadow: 'none',

                    borderRadius: '8px !important',

                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                    sx={{
                      backgroundColor:
                        theme.palette.mode === 'dark' ? theme.palette.grey[800] : 'white',
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                      },
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight={600}>
                      Requirements
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3, bgcolor: theme.palette.background.neutral }}>
                    <Stack spacing={3}>
                      {/* Special Request Field */}
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{
                            mb: 1,
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                          }}
                        >
                          Special request
                        </Typography>
                        <TextField
                          fullWidth
                          multiline
                          rows={1}
                          placeholder="Special request"
                          value={specialRequest}
                          onChange={(e) => setSpecialRequest(e.target.value)}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor:
                                theme.palette.mode === 'dark' ? theme.palette.grey[900] : '#F9FAFB',
                              '& fieldset': {
                                borderColor: theme.palette.divider,
                              },
                              '&:hover fieldset': {
                                borderColor: theme.palette.primary.main,
                              },
                            },
                          }}
                        />
                      </Box>
                    </Stack>
                  </AccordionDetails>
                </Accordion>
              </Card>
            )}
          </Card>

          {/* Continue Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
            <AppButton
              label="Continue"
              variant="contained"
              color="primary"
              fullWidth={false}
              isLoading={isPending}
              disabled={!areAllToolsLinked()}
              onClick={handleContinueToTheChat}
              sx={{
                minWidth: 120,
                height: 40,
                borderRadius: 2,
                textTransform: 'capitalize',
                fontWeight: 600,
                backgroundColor: '#9C6FE4',
                '&:hover': {
                  backgroundColor: '#8B5CF6',
                },
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* Configuration Dialog */}
      {selectedTool && (
        <ToolConfigDialog
          open={configDialogOpen}
          onClose={handleCloseConfigDialog}
          toolName={selectedTool?.tool.name}
          toolIcon={selectedTool?.tool?.icon || ''}
          connectedAccounts={[]}
        />
      )}

      {/* {authUrl && <AuthModal url={authUrl} onClose={() => setAuthUrl(null)} />} */}

      {stateTool && code && (
        <ConfigureToolsDialog handleToolConfigSelect={handleToolConfigSelect} />
      )}
    </>
  );
};

export default AgentCloneView;
