import {
  <PERSON>,
  Stack,
  Typo<PERSON>,
  <PERSON><PERSON>ield,
  Switch,
  Avatar,
  CircularProgress,
  <PERSON>ert,
  Button,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { useTranslation } from 'react-i18next';
import { LoadingScreen } from 'src/components/loading-screen';

import { AppButton } from 'src/components/common';
import { useProfileView } from './use-profile-view';

// ----------------------------------------------------------------------

export function ProfileSettings() {
  const { t } = useTranslation();

  const {
    userProfile,
    profileForm,
    avatarPreview,
    isLoadingProfile,
    isUpdatingProfile,
    profileError,
    updateError,
    isEditing,
    hasProfileChanges,
    handleProfileFormChange,
    handleAvatarChange,
    handleUpdateProfile,
    handleToggleEdit,
    isPendingFile,
    isPendingTogggleDeveloperMode,
    mutateToggleDeveloperMode,

    setDeveloperMode,
    developerMode,
  } = useProfileView();

  // Show loading state
  if (isLoadingProfile) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (profileError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading profile: {profileError.message}
      </Alert>
    );
  }

  return (
    <Stack spacing={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h4">Profile</Typography>
      </Box>

      {/* Show update error if any */}
      {updateError && <Alert severity="error">Error updating profile: {updateError.message}</Alert>}

      {/* Profile Picture */}
      <Stack spacing={1}>
        <Typography variant="subtitle2">Profile Pictures</Typography>
        <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
          <Stack direction="column">
            <Stack direction="row" alignItems="center">
              <Avatar
                alt="Profile Picture"
                src={avatarPreview || userProfile?.pictureUrl || '/path/to/your/image.jpg'}
                sx={{ width: 80, height: 80 }}
              />
              <Typography variant="caption" px="10px" color="text.secondary">
                At least 800x800 px recommended. JPG or PNG and GIF is allowed
              </Typography>
            </Stack>
          </Stack>

          <LoadingButton variant="outlined" component="label" loading={isPendingFile}>
            Upload new image
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(e) => handleAvatarChange(e.target.files?.[0] || null)}
            />
          </LoadingButton>
        </Stack>
      </Stack>

      {/* Input Fields */}
      <Stack spacing={2}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('First Name')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value={profileForm.firstName}
              onChange={(e) => handleProfileFormChange('firstName', e.target.value)}
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>

          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Last Name')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value={profileForm.lastName}
              onChange={(e) => handleProfileFormChange('lastName', e.target.value)}
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Username')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value={profileForm.username}
              onChange={(e) => handleProfileFormChange('username', e.target.value)}
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>

          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Email')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              type="email"
              value={userProfile?.email || ''}
              disabled
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>

        {/* Info about editable fields */}
        {isEditing && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Only Name and Username can be updated.
          </Alert>
        )}
      </Stack>

      {/* Developer Mode */}
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="subtitle1">Developer Mode</Typography>
          {isPendingTogggleDeveloperMode ? (
            <LoadingScreen />
          ) : (
            <>
              <Switch
                onClick={() =>
                  mutateToggleDeveloperMode(
                    {},
                    {
                      onSuccess: () => {
                        setDeveloperMode(!developerMode);
                      },
                    }
                  )
                }
                sx={{ mt: '35px' }}
                checked={developerMode}
              />
            </>
          )}
        </Stack>
        <Typography variant="body2" color="text.secondary">
          Enable the developer mode to use advanced tools like connecting a custom database.
        </Typography>
      </Stack>

      {/* Save Changes Button - Only show when editing */}

      <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ pt: 2 }}>
        <AppButton
          variant="contained"
          onClick={handleUpdateProfile}
          sx={{
            maxWidth: '120px',
            borderRadius: 1,
            bgcolor: 'primary.main',
          }}
          label={isUpdatingProfile ? 'Saving...' : 'Save Changes'}
        />
      </Stack>
    </Stack>
  );
}
