import { Card } from '@mui/material';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { TemplatesView } from 'src/sections/teams/view-templates/templates-view';

// ----------------------------------------------------------------------

export default function AgentsPage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{t('components.navigation.agents')} | Workforces</title>
      </Helmet>
      <Card sx={{ padding: '24px', mx: '12px' }}>
        <TemplatesView />
      </Card>
    </>
  );
}
