# .gitlab-ci.frontend.yml

# ------------------------------------- build (no deploy) ------------------------------------------

'build workforces-user-fe without deploy':
  stage: build
  cache:
    key:
      prefix: workforces-user-fe
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - node_modules
  variables:
    SERVICE_NAME: workforces-user-fe
    SERVICE_ENV: dev
  environment:
    name: ${SERVICE_NAME}-${SERVICE_ENV}
  script:
    - npm install
    - npm run build
    - '[ -f "dist/index.html" ] || { echo "Error: dist/index.html missing"; exit 1; }'
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

# ------------------------------------- build + deploy base ------------------------------------------

'.build then deploy workforces-user-fe':
  stage: build
  needs: ['prepare-version']
  cache:
    key:
      prefix: workforces-user-fe
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - node_modules
  variables:
    SERVICE_NAME: workforces-user-fe
    # SERVICE_ENV is set in the extending jobs below
  # before_script: *same as above*    # you can DRY this with YAML anchors if you like
  script:
    - npm install
    - npm run build
    - npm install wrangler
    - yarn wrangler pages deploy dist/ --project-name="${SERVICE_NAME}-${SERVICE_ENV}" --branch=main --commit-hash="$CI_COMMIT_SHORT_SHA" --commit-message="${CI_COMMIT_DESCRIPTION}" --commit-dirty=true

# ------------------------------------- dev / staging / prod ------------------------------------------

'build then deploy workforces-user-fe to dev':
  extends: '.build then deploy workforces-user-fe'
  variables:
    SERVICE_ENV: dev
  environment:
    name: ${SERVICE_NAME}-${SERVICE_ENV}
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/dev" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
    - if: '$CI_COMMIT_BRANCH == "stack/dev" && $APP_NAMES =~ /\bworkforces-user-fe\b/'

'build then deploy workforces-user-fe to staging':
  extends: '.build then deploy workforces-user-fe'
  variables:
    SERVICE_ENV: staging
  environment:
    name: ${SERVICE_NAME}-${SERVICE_ENV}
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && $APP_NAMES =~ /\bworkforces-user-fe\b/'    

'build then deploy workforces-user-fe to prod':
  extends: '.build then deploy workforces-user-fe'
  variables:
    SERVICE_ENV: prod
  environment:
    name: ${SERVICE_NAME}-${SERVICE_ENV}
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && $APP_NAMES =~ /\bworkforces-user-fe\b/'       

