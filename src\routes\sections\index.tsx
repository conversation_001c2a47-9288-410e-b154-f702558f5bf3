import { Navigate, useRoutes } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { useAuthContext } from 'src/auth/hooks';
import { paths } from 'src/routes/paths';

import { authRoutes } from './auth';
import { mainRoutes } from './main';
import { dashboardRoutes } from './dashboard';

// ----------------------------------------------------------------------

function RootRedirect() {
  const { authenticated, loading } = useAuthContext();

  if (loading) {
    return null; // Let the auth provider handle loading state
  }

  // If authenticated, redirect to dashboard, otherwise to sign-in
  const redirectPath = authenticated ? paths.dashboard.root : paths.auth.jwt.signIn;
  return <Navigate to={redirectPath} replace />;
}

export function Router() {
  return useRoutes([
    {
      path: '/',
      element: <RootRedirect />,
    },

    // Auth
    ...authRoutes,

    // Dashboard
    ...dashboardRoutes,

    // Main
    ...mainRoutes,

    // No match
    { path: '*', element: <Navigate to="/404" replace /> },
  ]);
}
