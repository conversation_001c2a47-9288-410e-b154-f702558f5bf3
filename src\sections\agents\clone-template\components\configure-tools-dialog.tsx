import { LoadingButton } from '@mui/lab';
import { Box, Dialog, InputLabel, TextField, Typography } from '@mui/material';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { useSearchParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAuthToolsApi } from 'src/services/api/use-authtools-api';

const ConfigureToolsDialog = ({
  handleToolConfigSelect,
}: {
  handleToolConfigSelect: (tooldConfigId: number, toolId: number, name: string) => void;
}) => {
  const [name, setName] = useState('');
  const [openDialog, setOpenDialog] = useState(true);

  const { useCreateAuthTools } = useAuthToolsApi();

  const { mutate, isPending } = useCreateAuthTools();
  const handleCancelTool = () => {
    localStorage.removeItem('state');
    localStorage.removeItem('code');
    setOpenDialog(false);
  };
  const handleAddTool = () => {
    const code = localStorage.getItem('code') || '';
    const state = localStorage.getItem('state') || '';
    const data = {
      code,
      state,
      name,
    };
    mutate(data, {
      onSuccess: (data) => {
        console.log('data', data?.data);
        // handleToolConfigSelect?.(data?.data?.id, data?.data?.toolId);
        handleToolConfigSelect(data?.data?.id, data?.data?.toolId, data?.data?.name);
        setOpenDialog(false);
        localStorage.removeItem('state');
        localStorage.removeItem('code');
      },
      onError: () => {},
    });
  };

  return (
    <Dialog
      open={openDialog}
      PaperProps={{
        sx: { width: '50%' },
        backgroundColor: '#F0F0F1',
      }}
    >
      <Box sx={{ padding: '20px' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h5">Connect Gmail Configuration</Typography>
          <Iconify
            sx={{ cursor: 'pointer' }}
            icon="material-symbols:close"
            onClick={handleCancelTool}
          />
        </Box>
        <Typography sx={{ mt: '20px' }}>Add your configuration name</Typography>
        <TextField
          fullWidth
          required
          placeholder="Configuration name"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
      </Box>
      <Box
        sx={{
          backgroundColor: '#E7E6EA',
          width: '100%',
          height: 'fit-content',
          borderTop: '1px solid #CDCAD5',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', p: '10px' }}>
          <AppButton
            fullWidth={false}
            onClick={handleCancelTool}
            sx={{ my: '10px', backgroundColor: 'white', color: 'black' }}
            label="cancel"
            variant="outlined"
          />
          <AppButton
            disabled={!name}
            fullWidth={false}
            onClick={handleAddTool}
            isLoading={isPending}
            sx={{ my: '10px' }}
            label="Save Configuration"
            variant="contained"
            color="primary"
          />
        </Box>
      </Box>
    </Dialog>
  );
};

export default ConfigureToolsDialog;
