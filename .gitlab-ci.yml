# .gitlab-ci.yml

stages:
  - sonarqube-check
  - pre
  - pre-publish
  - build
  - test
  - publish
  - post-publish
  - deploy

image: node:lts

# Cache between jobs
cache:
  key: $CI_COMMIT_REF_SLUG
  paths:
    - $HOME/.npm/
    - node_modules/
    - .yarn/cache/

# Reusable rule sets
.dev_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/dev" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

.staging_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

.prod_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

# ------------------------------------- SonarQube ------------------------------------------

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # cache location
    GIT_DEPTH: "0"                              # fetch full history
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

# ------------------------------------- pre ------------------------------------------

prepare-version:
  stage: pre
  image: alpine
  script:
    - export APP_ENV=${CI_COMMIT_BRANCH/stack\//}
    - export APP_VERSION=$(date +%Y.%m.%d)-${APP_ENV}_${CI_COMMIT_SHORT_SHA}
    - echo "APP_ENV=$APP_ENV" > build.env
    - echo "APP_VERSION=$APP_VERSION" >> build.env
    - echo "SERVICE_VERSION=$APP_VERSION" >> build.env
    - echo "SERVICE_BUILD_ENV=$APP_ENV" >> build.env
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 week

# (you can add pre-publish jobs here if you need)

# ------------------------------------- include frontend definitions ------------------------------------------

include:
  - local: "/.gitlab-ci.frontend.yml"
