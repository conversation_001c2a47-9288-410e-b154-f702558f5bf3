import { createContext, useContext, useState, useEffect, useMemo, ReactNode } from 'react';

// Define the context type
interface DeveloperModeContextType {
  developerMode: boolean;
  setDeveloperMode: (mode: boolean) => void;
}

// Create the context with default values
const DeveloperModeContext = createContext<DeveloperModeContextType>({
  developerMode: false,
  setDeveloperMode: () => {},
});

// Create a provider component
interface DeveloperModeProviderProps {
  children: ReactNode;
}

export function DeveloperModeProvider({ children }: DeveloperModeProviderProps) {
  // Initialize state from localStorage or default to false
  const [developerMode, setDeveloperModeState] = useState<boolean>(() => {
    try {
      const storedValue = localStorage.getItem('developerMode');
      return storedValue ? JSON.parse(storedValue) : false;
    } catch (error) {
      console.error('Error reading developerMode from localStorage:', error);
      return false;
    }
  });

  // Update localStorage when developerMode changes
  const setDeveloperMode = (mode: boolean) => {
    setDeveloperModeState(mode);
    console.log('mode', mode);
    try {
      localStorage.setItem('developerMode', JSON.stringify(mode));
    } catch (error) {
      console.error('Error saving developerMode to localStorage:', error);
    }
  };

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(
    () => ({
      developerMode,
      setDeveloperMode,
    }),
    [developerMode]
  );

  return <DeveloperModeContext.Provider value={value}>{children}</DeveloperModeContext.Provider>;
}

// Custom hook to use the developer mode context
export function useDeveloperMode() {
  const context = useContext(DeveloperModeContext);
  if (context === undefined) {
    throw new Error('useDeveloperMode must be used within a DeveloperModeProvider');
  }
  return context;
}
