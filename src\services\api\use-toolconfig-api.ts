import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const AgentsEndpoints = {
  list: '/tools-config',
  details: '/tools-config',
};

export interface ToolConfigsType {
  toolsConfigs: {
    id: number;
    userId: number;
    toolId: number;
    name: string;
    config: any;
    createdAt: string;
    updatedAt: string;
  }[];
}
// Define the Category interface

// Define the API response structure

// Create a hook to use the Agentss API
export const useToolConfigApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss with optional filters
  const useGetToolConfigs = (filters?: { toolId: string; name?: string }) => {
    return apiServices.useGetListService<ToolConfigsType, { toolId: string; name?: string }>({
      url: AgentsEndpoints.list,
      params: filters,
      queryOptions: {
        enabled: filters ? (filters?.toolId?.length > 0 ? true : false) : false,
      },
    });
  };

  // Get a single Agents by ID
  const useGetConfig = (id: string) => {
    return apiServices.useGetItemService<any>({
      url: AgentsEndpoints.details,
      id,
    });
  };

  return {
    useGetToolConfigs,
    useGetConfig,
  };
};
