import { useEffect, useState, memo } from 'react';
import { Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// Custom cursor component that follows the mouse and creates a light/lamp effect
export const CustomCursor = memo((): JSX.Element | null => {
  const theme = useTheme();
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const [isOverCard, setIsOverCard] = useState(false);

  useEffect(() => {
    // Function to update cursor position
    const updatePosition = (e: MouseEvent): void => {
      setPosition({ x: e.clientX, y: e.clientY });
    };

    // Function to check if cursor is over a card
    const checkIfOverCard = (e: MouseEvent): void => {
      // Check if the element or its parents have the data-cursor-light attribute
      let element = e.target as HTMLElement | null;
      let isOverCardElement = false;

      while (element) {
        if (element.hasAttribute('data-cursor-light')) {
          isOverCardElement = true;
          break;
        }
        element = element.parentElement;
      }

      setIsOverCard(isOverCardElement);
      setIsVisible(true);
    };

    // Function to handle mouse leaving the window
    const handleMouseLeave = (): void => {
      setIsVisible(false);
    };

    // Function to handle mouse entering the window
    const handleMouseEnter = (): void => {
      setIsVisible(true);
    };

    // Add event listeners
    document.addEventListener('mousemove', updatePosition);
    document.addEventListener('mousemove', checkIfOverCard);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseenter', handleMouseEnter);

    // Remove event listeners on cleanup
    return () => {
      document.removeEventListener('mousemove', updatePosition);
      document.removeEventListener('mousemove', checkIfOverCard);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, []);

  // Don't render if not visible
  if (!isVisible) return null;

  // Get primary color from theme
  const primaryColor = theme.palette.primary.main;

  return (
    <>
      {/* Large outer glow - only visible when over a card */}
      {isOverCard && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            pointerEvents: 'none',
            zIndex: 9999,
            transform: `translate(${position.x}px, ${position.y}px) translate(-50%, -50%)`,
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            background:
              'radial-gradient(circle, rgba(255, 140, 50, 0.1) 0%, rgba(255, 100, 30, 0.03) 50%, transparent 80%)',
            opacity: 0.7,
            transition: 'width 0.3s, height 0.3s',
            filter: 'blur(5px)',
            animation: 'firePulse 3s infinite ease-in-out',
          }}
        />
      )}

      {/* Medium fire glow - only visible when over a card */}
      {isOverCard && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            pointerEvents: 'none',
            zIndex: 9999,
            transform: `translate(${position.x}px, ${position.y}px) translate(-50%, -50%)`,
            width: '50px',
            height: '50px',
            borderRadius: '50%',
            background:
              'radial-gradient(circle, rgba(255, 120, 30, 0.2) 0%, rgba(255, 80, 10, 0.1) 60%, transparent 90%)',
            opacity: 0.8,
            transition: 'width 0.25s, height 0.25s',
            filter: 'blur(2px)',
            animation: 'firePulse 2.5s infinite ease-in-out 0.5s',
          }}
        />
      )}

      {/* Inner cursor - always visible */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 10000,
          transform: `translate(${position.x}px, ${position.y}px) translate(-50%, -50%)`,
          width: isOverCard ? '10px' : '8px',
          height: isOverCard ? '10px' : '8px',
          borderRadius: '50%',
          backgroundColor: isOverCard ? 'rgba(255, 100, 30, 0.9)' : 'rgba(255, 255, 255, 0.5)',
          boxShadow: isOverCard
            ? '0 0 10px 2px rgba(255, 120, 50, 0.6), 0 0 20px 5px rgba(255, 80, 10, 0.3)'
            : 'none',
          transition: 'width 0.2s, height 0.2s, background-color 0.2s, box-shadow 0.2s',
          animation: isOverCard ? 'fireFlicker 2s infinite ease-in-out' : 'none',
        }}
      />
    </>
  );
});

export default CustomCursor;
