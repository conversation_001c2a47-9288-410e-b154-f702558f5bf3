/**
 *  @type {import('eslint').ESLint.ConfigData}
 */
module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  plugins: ['perfectionist', 'unused-imports', '@typescript-eslint', 'prettier'],
  extends: ['airbnb', 'airbnb-typescript', 'airbnb/hooks', 'prettier'],
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 'latest',
    ecmaFeatures: { jsx: true },
    project: './tsconfig.json',
  },
  settings: {
    'import/resolver': {
      typescript: {
        project: './tsconfig.json',
      },
    },
  },
  /**
   * 0 ~ 'off'
   * 1 ~ 'warn'
   * 2 ~ 'error'
   */
  rules: {
    // general
    'no-alert': 0,
    camelcase: 0,
    'no-console': 0,
    'no-unused-vars': 0,
    'no-nested-ternary': 0,
    'no-param-reassign': 0,
    'no-underscore-dangle': 0,
    'no-restricted-exports': 0,
    'no-promise-executor-return': 0,
    'import/prefer-default-export': 0,
    'import/no-extraneous-dependencies': 0, // Disable external dependency checks
    'prefer-destructuring': 0,
    'prefer-template': 0, // Disable template string enforcement
    'arrow-body-style': 0, // Disable arrow function body style enforcement
    'no-unneeded-ternary': 0, // Disable ternary checks
    'no-unused-expressions': 0, // Disable unused expression checks

    // typescript
    '@typescript-eslint/naming-convention': 0,
    '@typescript-eslint/no-use-before-define': 0,
    '@typescript-eslint/consistent-type-exports': 0,
    '@typescript-eslint/consistent-type-imports': 0, // Disable type import enforcement
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/no-shadow': 0, // Disable shadow variable checks

    // react
    'react/no-children-prop': 0,
    'react/react-in-jsx-scope': 0,
    'react/no-array-index-key': 0,
    'react/require-default-props': 0,
    'react/jsx-props-no-spreading': 0,
    'react/function-component-definition': 0,
    'react/jsx-no-duplicate-props': [0, { ignoreCase: false }],
    'react/jsx-no-useless-fragment': 0, // Disable useless fragment checks
    'react/no-unstable-nested-components': 0,
    'react/prop-types': 0, // Disable prop-types validation for TypeScript projects
    'react-hooks/exhaustive-deps': 0, // Disable exhaustive deps checks

    // jsx-a11y
    'jsx-a11y/anchor-is-valid': 0,
    'jsx-a11y/control-has-associated-label': 0,

    // unused imports
    'unused-imports/no-unused-imports': 0,
    'unused-imports/no-unused-vars': [
      0,
      { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' },
    ],

    // perfectionist
    'perfectionist/sort-exports': 0, // Disable export sorting
    'perfectionist/sort-named-imports': 0, // Disable named import sorting
    'perfectionist/sort-named-exports': 0, // Disable named export sorting
    'perfectionist/sort-imports': 0, // Disable import sorting completely
  },
};
