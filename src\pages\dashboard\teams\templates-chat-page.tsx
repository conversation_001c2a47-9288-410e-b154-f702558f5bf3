import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Field } from 'src/components/hook-form/fields';

import { _account } from 'src/layouts/config-nav-account';
import { _notifications } from 'src/_mock/_notifications';

// import { AppContainer } from 'src/components/common';

import TeamTemplatesChat from 'src/sections/teams/chat/agents-chat';

// ----------------------------------------------------------------------

export default function MyTemplatesPage() {
  return (
    <>
      <Helmet>
        <title>Chat with agent Midad AI</title>
      </Helmet>
      <TeamTemplatesChat />
    </>
  );
}
