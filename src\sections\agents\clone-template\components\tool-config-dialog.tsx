import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  IconButton,
  Stack,
  Divider,
  Radio,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common/app-button';
import { GoogleAccountSelector, GoogleUser } from 'src/components/google-account-selector';

// ----------------------------------------------------------------------

interface ConnectedAccount {
  id: string;
  email: string;
  provider: string;
  icon: string;
}

interface ToolConfigDialogProps {
  open: boolean;
  onClose: () => void;
  toolName: string;
  toolIcon: string;
  connectedAccounts?: ConnectedAccount[];
}

export function ToolConfigDialog({
  open,
  onClose,
  toolName,
  toolIcon,
  connectedAccounts = [],
}: ToolConfigDialogProps) {
  const theme = useTheme();
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [showGoogleSelector, setShowGoogleSelector] = useState(false);
  const [localConnectedAccounts, setLocalConnectedAccounts] =
    useState<ConnectedAccount[]>(connectedAccounts);

  const handleAddNewAccount = () => {
    setShowGoogleSelector(true);
  };

  const handleGoogleAccountSelect = (googleUser: GoogleUser) => {
    // Add the selected Google account to the connected accounts list
    const newAccount: ConnectedAccount = {
      id: googleUser.id,
      email: googleUser.email,
      provider: 'Google',
      icon: 'logos:google',
    };

    // Check if account is already connected
    const isAlreadyConnected = localConnectedAccounts.some((acc) => acc.email === googleUser.email);

    if (!isAlreadyConnected) {
      setLocalConnectedAccounts((prev) => [...prev, newAccount]);
    }

    // Select the account
    setSelectedAccount(googleUser.id);
  };

  const handleContinue = () => {
    // Handle continue logic here
    onClose();
  };

  const handleCancel = () => {
    setShowGoogleSelector(false);
    setSelectedAccount('');
    onClose();
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: '#F0F0F1',
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" fontWeight={600}>
              Connect {toolName}
            </Typography>
            <IconButton onClick={onClose} size="small">
              <Iconify icon="eva:close-fill" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ py: 0 }}>
          <Stack spacing={3}>
            <Typography variant="body2" fontWeight={500} color="text.primary">
              Connected accounts
            </Typography>

            {/* Connected Accounts List */}
            <Stack spacing={2}>
              {localConnectedAccounts.length > 0 ? (
                localConnectedAccounts.map((account) => (
                  <Box
                    key={account.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      p: 2,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1,
                    }}
                  >
                    <Box display="flex" alignItems="center" gap={2}>
                      <Iconify icon={account.icon} width={24} height={24} />
                      <Typography variant="body2">{account.email}</Typography>
                    </Box>
                    <Radio
                      checked={selectedAccount === account.id}
                      onChange={() => setSelectedAccount(account.id)}
                      size="small"
                    />
                  </Box>
                ))
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ textAlign: 'center', py: 2 }}
                >
                  No accounts connected yet. Click &quot;Add new account&quot; to get started.
                </Typography>
              )}

              {/* Add New Account Button */}
              <AppButton
                fullWidth={false}
                variant="contained"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={handleAddNewAccount}
                sx={{
                  justifyContent: 'flex-start',
                  width: '33%',
                  color: 'text.secondary',
                  background: 'white',
                  borderColor: '#CBC9CF',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    backgroundColor: theme.palette.action.hover,
                  },
                }}
                label="Add new account"
              />
            </Stack>
          </Stack>
        </DialogContent>

        <DialogActions
          sx={{
            p: 3,
            pt: 2,
            gap: 2,
            background: '#E7E6E9',
            borderTop: '1px solid #CDCAD5',
            mt: '24px',
          }}
        >
          <Button onClick={handleCancel} color="inherit" variant="outlined">
            Cancel
          </Button>
          <AppButton
            label="Continue"
            variant="contained"
            color="primary"
            fullWidth={false}
            onClick={handleContinue}
            sx={{
              minWidth: 100,
              backgroundColor: '#9C6FE4',
              '&:hover': {
                backgroundColor: '#8B5CF6',
              },
            }}
          />
        </DialogActions>
      </Dialog>

      {/* Google Account Selector */}
      <GoogleAccountSelector
        open={showGoogleSelector}
        onClose={() => setShowGoogleSelector(false)}
        onAccountSelect={handleGoogleAccountSelect}
        selectedAccounts={localConnectedAccounts.map((acc) => ({
          id: acc.id,
          email: acc.email,
          name: acc.email.split('@')[0], // Use email prefix as name fallback
          provider: acc.provider,
        }))}
      />
    </>
  );
}
