import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';
import { useS3UploadService } from '../hooks/useS3UploadService';
import { useGetPictureurl } from '../hooks/use-get-picture-url';

// Define the API endpoints for user profile
export const profileEndpoints = {
  me: '/users/me',
  changePassword: '/users/change-password',
  file: '/users/generate-profile-upload-url',
  toggleDeveloperMode: '/users/toggle-developer-mode',
};

// Define the User Profile data type (based on actual API response)
export interface UserProfile {
  id: number;
  misrajId: string | null;
  email: string;
  name: string;
  username: string;
  pictureUrl: string | null;
  role: string;
  developerMode: boolean;
  createdAt: string;
  updatedAt: string;
  pictureFileId: string | null;
  verifiedAt: string | null;
  pictureFile: {
    id: number;
    contentType: string;
    createdAt: string;
    fileName: string;
    key: string;
    size: number;
    updatedAt: string;
  };
}

// Define the update profile payload type (only fields accepted by backend)
export interface UpdateProfilePayload {
  name?: string;
  username?: string;
  pictureUrl?: string;
}

// Define the UI form data type (for form management)
export interface ProfileFormData {
  name: string;
  username: string;
  pictureUrl?: string;
  // Additional fields for UI display (split from name)
  firstName: string;
  lastName: string;
}

// Define the change password payload type (for API request)
export interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}

// Define the change password form type (for UI form)
export interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Define the change password response type
export interface ChangePasswordResponse {
  message: string;
  success: boolean;
}

// Define the delete account response type
export interface DeleteAccountResponse {
  message: string;
  success: boolean;
}
export interface GenerateUploadFile {
  name: string;
  contentType: string;
  contentLength: number;
}

// Create a hook to use the profile API
export const useProfileApi = () => {
  const apiServices = useApiServices({ axiosInstance });
  const { generateDownloadUrl } = useGetPictureurl(axiosInstance);
  // Get current user profile
  const useGetUserProfile = () => {
    return apiServices.useGetItemService<UserProfile>({
      url: profileEndpoints.me,
    });
  };

  // Update user profile
  const useUpdateUserProfile = (onSuccess?: (data: UserProfile) => void) => {
    return apiServices.usePatchService<UpdateProfilePayload>({
      url: profileEndpoints.me,
      withFormData: false,
      onSuccess,
      queryKey: profileEndpoints.me + 'item',
    });
  };

  const useToggleDeveloperMode = (onSuccess?: (data: UserProfile) => void) => {
    return apiServices.usePatchService<UpdateProfilePayload>({
      url: profileEndpoints.toggleDeveloperMode,
      withFormData: false,
      onSuccess,
      queryKey: profileEndpoints.me + 'item',
    });
  };

  // Delete user account
  const useDeleteUserAccount = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<DeleteAccountResponse, void>({
      url: profileEndpoints.me,
      onSuccess,
    });
  };

  // Change password
  const useChangePassword = (onSuccess?: (data: ChangePasswordResponse) => void) => {
    return apiServices.usePostService<ChangePasswordPayload, ChangePasswordResponse>({
      url: profileEndpoints.changePassword,
      withFormData: false,
      onSuccess,
    });
  };

  const { useUploadFile } = useS3UploadService({ axiosInstance });

  return {
    useGetUserProfile,
    useUpdateUserProfile,
    useDeleteUserAccount,
    useChangePassword,
    useUploadFile,
    generateDownloadUrl,
    useToggleDeveloperMode,
  };
};
