import { Box, Typography } from '@mui/material';
import React from 'react';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';

const VerifyEmailPopup = ({ onClose }: { onClose: () => void }) => {
  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          background: '#EAEAEB',
          padding: '20px',
          height: '150px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: '20px',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              background: '#E7DED9',
              width: '60px',
              height: '60px',
              borderRadius: '40%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flex: 1,
            }}
          >
            <Iconify icon="mingcute:warning-line" />
          </Box>
          <Box>
            <Typography sx={{ fontWeight: 700, fontSize: '16px' }}>Email confirmation</Typography>
            <Typography>We have sent you an email to confirm your registration process</Typography>
          </Box>
        </Box>
        <Box onClick={onClose}>
          <Iconify icon="material-symbols-light:close-rounded" />
        </Box>
      </Box>
      <Box
        sx={{
          backgroundColor: '#E7E5EA',
          display: 'flex',
          justifyContent: 'flex-end',
          padding: '20px',
          borderTop: '1px solid #CDCAD5',
        }}
      >
        <AppButton
          fullWidth={false}
          label="cancel"
          variant="outlined"
          color="primary"
          onClick={onClose}
        />
      </Box>
    </>
  );
};

export default VerifyEmailPopup;
