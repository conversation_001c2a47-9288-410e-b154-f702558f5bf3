import { useState, useEffect } from 'react';
import { Box, Paper, IconButton, alpha, Divider, TextField, Autocomplete } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { useGlobalSearch } from 'src/hooks/use-global-search';

// ----------------------------------------------------------------------

export interface GoogleSearchbarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  options?: string[];
  freeSolo?: boolean;
}

export function GoogleSearchbar({
  placeholder = 'Search...',
  onSearch,
  options = [],
  freeSolo = true,
}: GoogleSearchbarProps) {
  const [query, setQuery] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<string[]>(options);
  const { handleGlobalSearch } = useGlobalSearch();
  const { t, i18n } = useTranslation();

  // Update placeholder based on current language
  const searchPlaceholder = placeholder || t('search.placeholder');

  useEffect(() => {
    setSearchOptions(options);
  }, [options]);

  const handleSearch = (event: React.FormEvent) => {
    event.preventDefault();
    if (onSearch) {
      onSearch(query);
    }
    // Use the global search functionality
    handleGlobalSearch(query);
  };

  const handleInputChange = (_event: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue);
  };

  const handleChange = (_event: React.SyntheticEvent, newValue: string | null) => {
    setQuery(newValue || '');
    if (newValue === null || newValue === '') {
      // Clear the search query from the URL when cleared
      handleGlobalSearch('');
    } else if (onSearch && newValue) {
      onSearch(newValue);
    }
  };

  const handleClearSearch = () => {
    setQuery('');
    setInputValue('');
    // Clear the search query from the URL by navigating to the current path without query
    handleGlobalSearch('');
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        width: '95%',
        mx: '20px',
        maxWidth: { xs: '100%', sm: 600, md: '95%' },
        px: { xs: 2, sm: 0 },
      }}
    >
      <Paper
        component="form"
        onSubmit={handleSearch}
        elevation={0}
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          borderRadius: 24,
          px: 2,
          py: 1,
          border: '1px solid',
          borderColor: (theme) =>
            theme.palette.mode === 'dark'
              ? alpha(theme.palette.common.white, 0.2)
              : alpha(theme.palette.grey[500], 0.32),
          bgcolor: (theme) =>
            theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.05) : 'white',
          '&:hover': {
            boxShadow: (theme) =>
              theme.palette.mode === 'dark'
                ? '0 1px 6px rgba(255,255,255,.15)'
                : '0 1px 6px rgba(32,33,36,.28)',
            borderColor: 'transparent',
          },
        }}
      >
        <Autocomplete
          freeSolo={freeSolo}
          options={searchOptions}
          inputValue={inputValue}
          onInputChange={handleInputChange}
          value={query}
          onChange={handleChange}
          fullWidth
          clearOnBlur={false}
          clearOnEscape
          disableClearable={false}
          sx={{ flex: 1 }}
          clearIcon={
            <Iconify
              icon="eva:close-fill"
              width={20}
              sx={{
                color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#5f6368'),
              }}
            />
          }
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder={searchPlaceholder}
              variant="standard"
              dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}
              InputProps={{
                ...params.InputProps,
                startAdornment: (
                  <IconButton sx={{ p: '5px' }} aria-label="search">
                    <Iconify
                      icon="eva:search-fill"
                      width={20}
                      sx={{
                        color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#5f6368'),
                      }}
                    />
                  </IconButton>
                ),
                disableUnderline: true,
                sx: {
                  fontSize: '16px',
                  color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#202124'),
                  '& .MuiAutocomplete-endAdornment': {
                    right: i18n.language === 'ar' ? 'auto' : 0,
                    left: i18n.language === 'ar' ? 0 : 'auto',
                  },
                  '& input::placeholder': {
                    color: (theme) =>
                      theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'inherit',
                    opacity: 1,
                  },
                },
              }}
            />
          )}
        />

        {/* Custom buttons can be added here if needed */}

        {/* <Tooltip title="Search by voice">
          <IconButton sx={{ p: '10px' }} aria-label="voice search">
            <Iconify icon="eva:mic-fill" width={20} color="#4285f4" />
          </IconButton>
        </Tooltip> */}
      </Paper>
    </Box>
  );
}

export * from './types';
